import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/command_line.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';
const _kFlagAbTest = '--ab-test';

const _kTargetDb = 'db';
const _kTargetDbTest = 'db-test';
const _kTargetDbControl = 'db-control';
const _kTargetLogs = 'logs';
const _kTargetPrefs = 'prefs';
const _kTargetBugReport = 'bug-report';

final _help = '''
A tool for dumping app data. Copies data to $_kFriendlyDirectory and opens it.

Works for:
 iOS Simulator
 Android Device
*A debuggable com.bitacora.locust app must be installed on device/simulator.


Available arguments:

 -h              Prints help.
 db [default]    Dumps $_kDbFilename
 logs            Dumps $_kLogsDirectory
 prefs           Dumps $_kPrefsFilename.[plist|xml]
 bug-report      Dumps $_kBugReportFilename
 db-test         Dumps A|B test db $_kDbTestFilename
 db-control      Dumps A|B control db $_kDbControlFilename
 --ab-test       Dumps A|B databases and inspects difference
 
Usage:
 
  bit dump
  bit dump logs
  bit dump db-test
  bit dump --ab-test
  bit dump prefs

''';

final _kFriendlyDirectory = '${Platform.environment['HOME']!}/Desktop/bit_dump';
const _kFriendlyAndroidDirectory = '/sdcard';

const _kDbFilename = 'bitacora.db';
const _kDbTestFilename = 'test.db';
const _kDbControlFilename = 'control.db';
const _kLogsDirectory = 'logs';
const _kPrefsFilename = 'prefs';
const _kBugReportFilename = 'bug_report.zip';

final timestamp = '${DateTime.now().millisecondsSinceEpoch}';

const _kTargets = {
  _kTargetDb: _DumpTarget(
    _kDbFilename,
    androidPath: 'databases/$_kDbFilename',
    iosPath: 'Library/$_kDbFilename',
  ),
  _kTargetDbTest: _DumpTarget(
    _kDbTestFilename,
    androidPath: 'databases/$_kDbTestFilename',
    iosPath: 'Library/$_kDbTestFilename',
  ),
  _kTargetDbControl: _DumpTarget(
    _kDbControlFilename,
    androidPath: 'databases/$_kDbControlFilename',
    iosPath: 'Library/$_kDbControlFilename',
  ),
  _kTargetLogs: _DumpTarget(
    _kLogsDirectory,
    isDirectory: true,
    androidPath: 'files/$_kLogsDirectory',
    iosPath: 'Library/Application Support/$_kLogsDirectory',
  ),
  _kTargetPrefs: _DumpTarget(
    _kPrefsFilename,
    androidPath: 'shared_prefs/FlutterSharedPreferences.xml',
    iosPath: 'Library/Preferences/com.bitacora.locust.plist',
  ),
  _kTargetBugReport: _DumpTarget(
    _kBugReportFilename,
    androidPath: 'cache/$_kBugReportFilename',
    iosPath: 'Library/Caches/$_kBugReportFilename',
  ),
};

Future<int> bitDump([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  if (args.isNotEmpty) {
    if (args[0] == _kFlagAbTest) {
      return _dumpAbTestAndCompare();
    } else if (args[0] == _kTargetPrefs) {
      return _dumpPrefsAndTranslate();
    }
    // FIXME: check argument... provide picker...
    return _dump(_kTargets[args[0]]!);
  }

  final dump = await CommandLine(
    _kTargets.map(
      (k, v) => MapEntry(
        k,
        k == _kTargetPrefs ? _dumpPrefsAndTranslate : () => _dump(v),
      ),
    ),
    title: 'Select target.',
  ).present();
  return dump ?? 1;
}

Future<int> _dump(
  _DumpTarget target, [
  bool open = true,
]) async {
  writeln('Dumping $target ...', TextStyle.highlighted);
  await runProcess('mkdir', [_kFriendlyDirectory]);
  final extracted = await _extract(target);
  if (extracted <= 0 && open) {
    if (extracted == -3) {
      final chosenExtract = await CommandLine(
        {
          'ios': () => _openExtracted(target, true),
          'android': () => _openExtracted(target, false),
        },
        title: 'Select dump extract.',
      ).present();
      return chosenExtract ?? 1;
    }
    return _openExtracted(target, (extracted * -1) % 2 == 1);
  }
  return extracted;
}

Future<int> _extract(_DumpTarget target) async {
  writeln('Copying $target to $_kFriendlyDirectory ...');
  final copies = await Future.wait(
    [
      _simulatorExtract(target),
      _androidExtract(target),
    ],
  );

  final numCopies = (copies[0] == 0 ? 1 : 0) + (copies[1] == 0 ? 1 : 0);

  if (numCopies == 0) {
    return 1;
  }

  if (numCopies > 1) {
    writeln('More than one `$target` found.');
    writeln();
    writeln('   ${target.outputPathFromIos}');
    writeln('   ${target.outputPathFromAndroid}');
  }

  return ((copies[0] == 0 ? 1 : 0) + (copies[1] == 0 ? 1 : 0) * 2) * -1;
}

Future<int> _openExtracted(_DumpTarget target, bool fromIos) async {
  writeln('Launching $target ...', TextStyle.highlighted);
  final openResult = await runProcess(
    'open',
    [fromIos ? target.outputPathFromIos : target.outputPathFromAndroid],
  );
  if (openResult != 0) {
    writeln('Failed to open $target', TextStyle.error);
  }
  return openResult;
}

Future<int> _androidExtract(_DumpTarget target) async {
  final deviceCopy = await _androidExtractFromApp(target);
  if (deviceCopy != 0) {
    writeln(
        'Failed to copy from Android device [adb shell cp]', TextStyle.error);
    return deviceCopy;
  }

  final adbPull = await _androidExtractFromDevice(target);
  if (adbPull != 0) {
    writeln('Failed to copy from Android device [adb pull]', TextStyle.error);
  }

  writeln('Found $target on Android device.', TextStyle.highlighted);
  return adbPull;
}

Future<int> _androidExtractFromApp(_DumpTarget target) async {
  final shell = await startProcess('adb', ['shell']);
  shell.stdout.listen((event) {
    writeln('[shell] $event');
  });
  shell.stdin.add(utf8.encode('run-as com.bitacora.locust\n'));
  shell.stdin.add(utf8.encode(
    'cp -R ${target.androidPath} '
    '${target.copyPathAndroid}\n',
  ));
  shell.stdin.add(utf8.encode('exit\n'));
  shell.stdin.add(utf8.encode('exit\n'));

  return shell.exitCode;
}

Future<int> _androidExtractFromDevice(_DumpTarget target) async {
  return runProcess('adb', [
    'pull',
    target.copyPathAndroid,
    target.outputPathFromAndroid,
  ]);
}

Future<int> _simulatorExtract(_DumpTarget target) async {
  final xcrun = await startProcess('xcrun', [
    'simctl',
    'get_app_container',
    'booted',
    'com.bitacora.locust',
    'data',
  ]);
  final xcRunResult = await xcrun.exitCode;
  if (xcRunResult != 0) {
    return 1;
  }

  final source =
      (await xcrun.stdout.transform(utf8.decoder).first).split('\n').first;

  final copy = await runProcess('cp', [
    if (target.isDirectory) '-R',
    '$source/${target.iosPath}',
    target.outputPathFromIos,
  ]);

  if (copy != 0) {
    writeln('Failed to copy from iOS Simulator', TextStyle.error);
    return 1;
  }
  writeln('Found $target on iOS Simulator.', TextStyle.highlighted);
  return 0;
}

class _DumpTarget {
  final String name;
  final bool isDirectory;
  final String androidPath;
  final String iosPath;

  const _DumpTarget(
    this.name, {
    required this.androidPath,
    required this.iosPath,
    this.isDirectory = false,
  });

  String get outputPathFromIos => '$_kFriendlyDirectory/ios_${timestamp}_$name';

  String get outputPathFromAndroid =>
      '$_kFriendlyDirectory/android_${timestamp}_$name';

  String get copyPathAndroid =>
      '$_kFriendlyAndroidDirectory/android_${timestamp}_$name';

  @override
  String toString() => name;
}

Future<int> _dumpPrefsAndTranslate() async {
  final target = _kTargets[_kTargetPrefs]!;
  final dump = (await _dump(target, false)) * -1;
  if (dump <= 0) {
    return -dump;
  }

  if (dump == 3) {
    final chosenDump = await CommandLine(
      {
        'ios': () => _printIosPrefs(target),
        'android': () => _printAndroidPrefs(target),
      },
      title: 'Select dump extract.',
    ).present();
    return chosenDump ?? 1;
  }

  if (dump % 2 == 1) {
    return _printIosPrefs(target);
  }

  if ((dump ~/ 2) % 2 == 1) {
    return _printAndroidPrefs(target);
  }
  return 0;
}

Future<int> _printIosPrefs(_DumpTarget target) async {
  final path = target.outputPathFromIos;
  await runProcess('mv', [
    path,
    '$path.plist',
  ]);
  return runProcess(
    'plutil',
    ['-p', '$path.plist'],
    ProcessStartMode.inheritStdio,
  );
}

Future<int> _printAndroidPrefs(_DumpTarget target) async {
  final path = target.outputPathFromAndroid;
  await runProcess('mv', [
    path,
    '$path.xml',
  ]);
  return runProcess(
    'more',
    ['$path.xml'],
    ProcessStartMode.inheritStdio,
  );
}

Future<int> _dumpAbTestAndCompare() async {
  writeln(
    'Dumping A|B test databases and inspecting difference ...',
    TextStyle.highlighted,
  );

  final control = _kTargets[_kTargetDbControl]!;
  final test = _kTargets[_kTargetDbTest]!;

  final controlDumpResult = await _dump(control, false);
  if (controlDumpResult < 0) {
    return controlDumpResult;
  }
  final testDumpResult = await _dump(test, false);
  if (testDumpResult != controlDumpResult) {
    return testDumpResult;
  }
  return _analyzeAbDifference(control, test, testDumpResult == 0);
}

Future<int> _analyzeAbDifference(
    _DumpTarget a, _DumpTarget b, bool fromIos) async {
  final sqldiff = await startProcess(
    'sqldiff',
    [
      (fromIos ? a.outputPathFromIos : a.outputPathFromAndroid),
      (fromIos ? b.outputPathFromIos : b.outputPathFromAndroid),
    ],
  );

  var foundDifference = false;
  sqldiff.stdout.listen((event) {
    foundDifference = true;
    stdout.add(event);
  });

  final result = await sqldiff.exitCode;
  if (!foundDifference) {
    writeln('Databases are identical.', TextStyle.highlighted);
  } else {
    writeln('Databases are different.', TextStyle.error);
  }
  return result;
}
