import 'dart:async';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/command_line.dart';
import 'package:bit/git_utils.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _help = '''
A tool for pushing a git branch.

If current branch is master, runs `git push` after a confirmation.

Otherwise, force pushes the branch after a confirmation.
 
i.e. `git push --set-upstream origin [current-branch] --force`

Usage: 

  bit git push
  bit push
''';

Future<int> bitGitPush([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  final branch = await gitGetCurrentBranch();
  writeln('Pushing \'$branch\'...', TextStyle.highlighted);

  return _maybePushBranch(branch);
}

Future<int> _maybePushBranch(String branch) async {
  final isMaster = branch == 'master';
  final force = !isMaster;

  final args = [
    'push',
    if (!isMaster) '--set-upstream',
    if (!isMaster) 'origin',
    if (!isMaster) branch,
    if (force) '--force',
  ];

  writeln('\n`git ${args.join(' ')}`', TextStyle.highlighted);
  final push = await CommandLine.confirm(
    () => runProcess(
      'git',
      args,
      ProcessStartMode.inheritStdio,
    ),
    yes: 'yes${force ? '-by-force' : ''}',
  ).present();
  return push ?? 1;
}
