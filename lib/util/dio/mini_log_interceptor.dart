import 'package:dio/dio.dart';

/// Copied from dio/interceptors/log.dart

/// [MiniLogInterceptor] is used to print logs during network requests.
/// It's better to add [MiniLogInterceptor] to the tail of the interceptor queue,
/// otherwise the changes made in the interceptor behind A will not be printed out.
/// This is because the execution of interceptors is in the order of addition.
class MiniLogInterceptor extends Interceptor {
  MiniLogInterceptor({
    this.logPrint = print,
  });

  /// Log printer; defaults print log to console.
  /// In flutter, you'd better use debugPrint.
  /// you can also write log in a file, for example:
  ///```dart
  ///  var file=File("./log.txt");
  ///  var sink=file.openWrite();
  ///  dio.interceptors.add(LogInterceptor(logPrint: sink.writeln));
  ///  ...
  ///  await sink.close();
  ///```
  void Function(Object object) logPrint;

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    logPrint(
        'dio:request ${options.uri} data:${_maybeObfuscateData(options.data)}');
    handler.next(options);
  }

  dynamic _maybeObfuscateData(dynamic data) {
    if (data is! Map<String, String>) {
      return data;
    }
    if (!data.containsKey('password')) {
      return data;
    }
    return {...data, 'password': '*****'};
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    logPrint(
      'dio:response ${response.requestOptions.uri}'
      ' ${response.statusCode}:${response.statusMessage}',
    );
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    logPrint('dio:error ${err.requestOptions.uri} $err');

    final response = err.response;
    if (response != null) {
      logPrint(
        ' dio:error:response ${response.statusCode}:${response.statusMessage}',
      );
    }

    handler.next(err);
  }
}
