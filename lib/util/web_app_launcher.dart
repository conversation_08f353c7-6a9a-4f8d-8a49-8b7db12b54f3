import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/recover_session/recover_session_launcher.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/url_launcher.dart';
import 'package:flutter/widgets.dart';
import 'package:url_launcher/url_launcher.dart';

class WebAppLauncher {
  factory WebAppLauncher() => inject(() => const WebAppLauncher._());

  const WebAppLauncher._();

  void maybeLaunchBitacoraWebApp(
    RecoverSessionLauncher recoverSessionLauncher,
    NavigatorState navigatorState,
    ActiveSession activeSession,
    String locale,
    String path, {
    bool allowUnauthenticated = false,
    LaunchMode launchMode = LaunchMode.inAppWebView,
  }) {
    recoverSessionLauncher.launchOrMaybeRun(
      navigatorState,
      activeSession,
      runIsValid: () async {
        final url = getUrl(activeSession, locale, path, allowUnauthenticated);
        if (url == null) {
          return;
        }

        final urlLauncher = UrlLauncher();
        final urlString = url.toString();
        if (await urlLauncher.canLaunch(urlString)) {
          await urlLauncher.launch(urlString, mode: launchMode);
        }
      },
      forceRecoverSessionUi: true,
    );
  }

  @visibleForTesting
  String? getUrl(
    ActiveSession activeSession,
    String locale,
    String path,
    bool allowUnauthenticated,
  ) {
    final token = activeSession.value?.token.value;
    if (token == null && !allowUnauthenticated) {
      logger.f('No session when launching external url');
      return null;
    }

    final webAppUrl = AppConfig().webAppUrl;

    if (token == null) {
      return '$webAppUrl/$path'
          '&locale=$locale'
          '&isFlutter=true';
    }
    return '$webAppUrl/auth'
        '?token=${Uri.encodeFull(token)}'
        '&url=/$path'
        '&locale=$locale'
        '&isFlutter=true';
  }
}
