import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model_type.dart';

class HasExistingOutgoingMutationForModelRepositoryQuery
    extends RepositoryQuery<bool> {
  final OutgoingMutationModelType modelType;
  final LocalId modelId;

  const HasExistingOutgoingMutationForModelRepositoryQuery(
      this.modelType, this.modelId);

  @override
  Future<bool> run(RepositoryQueryContext context) =>
      context.db.outgoingMutation
          .hasExistingOutgoingMutationForModel(context, modelType, modelId);

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) {
    return db.outgoingMutation.fieldsBuilder.build();
  }
}
