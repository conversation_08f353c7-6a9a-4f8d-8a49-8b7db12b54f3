import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/analytics/analytics_widget.dart';
import 'package:bitacora/analytics/bitacora_analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/auth_app_service.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/metadata/active_custom_field_metadata_filter.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/organization/organization_cache.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/application/cache/template/template_cache.dart';
import 'package:bitacora/application/last_foreground/last_foreground.dart';
import 'package:bitacora/application/location_tracking/widget/location_tracking_monitor_widget.dart';
import 'package:bitacora/application/recover_session/recover_session_launcher.dart';
import 'package:bitacora/application/remediation/remediation_widget.dart';
import 'package:bitacora/application/router.dart';
import 'package:bitacora/application/sync/sync_widget.dart';
import 'package:bitacora/dev/command/hot_reload_command.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/infrastructure/auth/auth_mixed_db_repository.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/presentation/home_page_selector.dart';
import 'package:bitacora/presentation/theme/active_theme_mode.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/util/connectivity/connectivity_widget.dart';
import 'package:bitacora/util/fcm/fcm_widget.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/restart_widget.dart';
import 'package:bitacora/util/route_observer.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../l10n/app_localizations.dart';

class BitacoraApp extends StatelessWidget {
  final Widget? home;

  const BitacoraApp({super.key, this.home});

  @override
  Widget build(BuildContext context) {
    logger.log(Level.info, 'bitacoraApp:build');

    return RestartWidget(
      child: MultiProvider(
        providers: [
          Provider<AnalyticsLogger>(
            create: (context) => BitacoraAnalyticsLogger(),
          ),
          Provider<Repository>(
            create: (context) => DbRepository(),
            dispose: (_, db) => db.close(),
          ),
          Provider<ApiTranslator>(
            create: (context) => ApiTranslator(),
          ),
          ChangeNotifierProvider<AuthRepository>(
            create: (context) =>
                AuthRepositoryInjector().get(context.read<Repository>()),
          ),
          ChangeNotifierProvider<ActiveSession>(
            create: (context) =>
                ActiveSessionInjector.get(context.read<AuthRepository>()),
          ),
          Provider<ApiHelper>(
            create: (context) => ApiHelperInjector().get(
              authRepository: context.read<AuthRepository>(),
            ),
          ),
          ChangeNotifierProvider<OrganizationCache>(
            create: (context) => OrganizationCache(
              context.read<ActiveSession>(),
              context.read<Repository>(),
            ),
          ),
          ChangeNotifierProvider<ActiveOrganization>(
            create: (context) =>
                ActiveOrganization(context.read<OrganizationCache>()),
          ),
          ChangeNotifierProvider<ActiveLogDay>(
            create: (context) => ActiveLogDay(),
          ),
          ChangeNotifierProvider<ProjectCache>(
            create: (context) => ProjectCache(
                context.read<ActiveOrganization>(),
                context.read<Repository>(),
                context.read<ActiveSession>()),
          ),
          ChangeNotifierProvider<ActiveProject>(
            create: (context) => ActiveProject(context.read<ProjectCache>()),
          ),
          ChangeNotifierProvider<TemplateCache>(
            create: (context) => TemplateCache(
              context.read<ActiveOrganization>(),
              context.read<Repository>(),
            ),
          ),
          ChangeNotifierProvider<ActiveCustomFieldMetadataFilter>(
            create: (context) => ActiveCustomFieldMetadataFilter(null),
          ),
          Provider<AuthAppService>(
            create: (context) => AuthAppService(
              apiHelper: context.read<ApiHelper>(),
              db: context.read<Repository>(),
              apiTranslator: context.read<ApiTranslator>(),
              authRepository: context.read<AuthRepository>(),
              analyticsLogger: context.read<AnalyticsLogger>(),
            ),
          ),
          ChangeNotifierProvider<ActiveThemeMode>(
            create: (context) => ActiveThemeMode(),
          ),
          Provider<RecoverSessionLauncher>(
              create: (_) => RecoverSessionLauncher()),
        ],
        child: _MaterialApp(home),
      ),
    );
  }
}

class _MaterialApp extends StatelessWidget {
  final Widget? home;

  const _MaterialApp(this.home);

  @override
  Widget build(BuildContext context) {
    final activeOrganization = context.watch<ActiveOrganization>();
    final primarySwatch = activeOrganization.value != null
        ? createMaterialColor(activeOrganization.value!.color!.value)
        : bitacoraGreen;
    final activeThemeMode = context.watch<ActiveThemeMode>();
    final analyticsLogger = context.read<AnalyticsLogger>();

    return LastForeground(
      child: ConnectivityWidget(
        child: SyncWidget(
          child: LocationTrackingMonitorWidget(
            child: FcmWidget(
              child: AnalyticsWidget(
                child: MaterialApp(
                  debugShowCheckedModeBanner: false,
                  localizationsDelegates:
                      AppLocalizations.localizationsDelegates,
                  supportedLocales: AppLocalizations.supportedLocales,
                  onGenerateRoute: onGenerateRoute,
                  home: RemediationWidget(
                    child: HotReloadCommand(
                      child: home ?? const HomePageSelectorWidget(),
                    ),
                  ),
                  theme: buildLightThemeData(primarySwatch),
                  darkTheme: buildDarkThemeData(primarySwatch),
                  themeMode: activeThemeMode.value,
                  navigatorObservers: [
                    RouteObserverInjector.get(),
                    analyticsLogger.navigatorObserver,
                    SentryNavigatorObserver(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
