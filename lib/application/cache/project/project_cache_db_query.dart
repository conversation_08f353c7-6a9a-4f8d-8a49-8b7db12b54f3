import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/project/project.dart';

class ProjectCacheDbQuery extends RepositoryQuery<List<Project>> {
  final LocalId organizationId;

  const ProjectCacheDbQuery(this.organizationId);

  @override
  Future<List<Project>> run(RepositoryQueryContext context) {
    return context.db.project.findWithAccess(context, organizationId);
  }

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.project.fieldsBuilder.name().remoteId().build();
}
