import 'dart:async';

import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/template/template_cache_db_query.dart';
import 'package:bitacora/application/hardcoded_data/template_directory/hardcoded_template_directory.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/util/loader/loader.dart';
import 'package:bitacora/util/throttler.dart';
import 'package:collection/collection.dart';

class TemplateCache extends Loader<List<Template>> {
  final ActiveOrganization _activeOrganization;
  late final StreamSubscription _mutationsSubscription;
  late final StreamSubscription _templateConditionsSubscription;
  final Throttler _throttler = ThrottlerInjector().get();

  TemplateCache(
    ActiveOrganization activeOrganization,
    Repository db,
  )   : _activeOrganization = activeOrganization,
        super(() async {
          if (activeOrganization.value == null) {
            return null;
          }
          final templates = await db.query(TemplateCacheDbQuery(
              organizationId: activeOrganization.value!.id!));

          return HardcodedTemplateDirectory.maybeRelateWithDirectories(
              activeOrganization, templates);
        }) {
    _activeOrganization.addListener(load);
    _mutationsSubscription = db.template.getMutations().listen((event) async {
      if (!_needsLoadFromMutationEvent(event)) {
        return;
      }
      _reload();
    });
    _templateConditionsSubscription =
        db.templateCondition.getMutations().listen((event) async {
      if (event.type != MutationType.insert) {
        return;
      }
      _reload();
    });
  }

  void _reload() {
    _throttler.add(() {
      // FIXME: wait for transaction to end to further improve load time
      /// i.e. SyncMachineHead has a transaction that might insert large
      /// number of projects, the first time the user logs in. This causes
      /// the load to happen multiple times, when the first load is already
      /// correct.
      load();
    });
  }

  bool _needsLoadFromMutationEvent(Mutation<Template> event) {
    if (value == null) {
      return true;
    }

    final model = event.model;
    switch (event.type.value) {
      case MutationTypeValue.insert:
        return model!.organization!.id == _activeOrganization.value?.id;
      case MutationTypeValue.update:
        if (model!.organization?.id != null &&
            model.organization?.id != _activeOrganization.value?.id) {
          return false;
        }

        final cachedModel =
            value?.firstWhereOrNull((element) => element.id == event.id);
        if (cachedModel == null) {
          return true;
        }

        return (cachedModel.remoteId != model.remoteId &&
                model.remoteId != null) ||
            (cachedModel.deletedAt != model.deletedAt &&
                model.deletedAt != null) ||
            (cachedModel.name != model.name && model.name != null);
      case MutationTypeValue.delete:
      case MutationTypeValue.unknown:
        return true;
    }
  }

  Template? find(LocalId id) {
    return value?.firstWhereOrNull((e) => e.id == id);
  }

  @override
  void dispose() {
    _activeOrganization.removeListener(load);
    _mutationsSubscription.cancel();
    _templateConditionsSubscription.cancel();
    super.dispose();
  }
}
