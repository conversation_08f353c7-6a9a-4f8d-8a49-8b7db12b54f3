import 'package:bitacora/application/sync/machine/steps/download/collection/entry_groups/entry_group_by_remote_id_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';

class SyncCollectionEntryGroupDownloader extends SyncCollectionDownloader {
  SyncCollectionEntryGroupDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.entryGroup;

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    return db.transaction<LocalId?>((dbContext) async {
      final entryGroupData = {
        ...map,
        'entries': null,
        'organization_id': params.organization.remoteId!.apiValue!,
      };
      final entryGroup = apiTranslator.entryGroup.fromMap(entryGroupData);
      final entryGroupId = await db.entryGroup.save(dbContext, entryGroup);

      final entryGroupWithEntries =
          apiTranslator.entryGroup.fromMap(map).copyWith(id: entryGroupId!);
      await db.entryGroupEntry.saveAll(
        dbContext,
        Mutation(
          id: entryGroupWithEntries.id!,
          type: MutationType.unknown,
          model: entryGroupWithEntries,
        ),
        true,
      );
      return entryGroupId;
    });
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    await db.transaction((context) async {
      final entryGroup = await db.query(
          EntryGroupByRemoteIdRepositoryQuery(remoteId),
          context: context);
      if (entryGroup != null) {
        await db.entryGroup.delete(context, entryGroup.id!);
      }
    });
  }
}
