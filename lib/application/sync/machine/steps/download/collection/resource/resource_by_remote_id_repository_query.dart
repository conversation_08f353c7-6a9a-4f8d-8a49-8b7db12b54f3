import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/resource/resource.dart';

class ResourceByRemoteIdRepositoryQuery extends RepositoryQuery<Resource?> {
  final RemoteId remoteId;

  const ResourceByRemoteIdRepositoryQuery(this.remoteId);

  @override
  Future<Resource?> run(RepositoryQueryContext context) =>
      context.db.resource.findByRemoteId(context, remoteId);

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.resource.fieldsBuilder.build();
}
