import 'package:bitacora/domain/avatar/avatar.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';

class AvatarDownloadRepositoryQuery extends RepositoryQuery<Avatar?> {
  final LocalId id;

  AvatarDownloadRepositoryQuery({required this.id});

  @override
  Future<Avatar?> run(RepositoryQueryContext context) =>
      context.db.avatar.find(context, id);

  @override
  Fields fields(Repository db) => db.avatar.fieldsBuilder
      .s3Key()
      .path()
      .isDownloaded()
      .transferState()
      .build();
}
