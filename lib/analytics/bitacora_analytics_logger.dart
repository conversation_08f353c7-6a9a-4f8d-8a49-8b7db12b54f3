import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/analytics/analytics_provider.dart';
import 'package:bitacora/analytics/firebase/firebase_analytics_provider.dart';
import 'package:bitacora/analytics/mixpanel/mixpanel_analytics_provider.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/widgets.dart';

class BitacoraAnalyticsLogger implements AnalyticsLogger {
  final List<AnalyticsProvider> _providers = AppConfig().isIntegrationTest
      ? []
      : [
          // FacebookAnalyticsProvider(), // FIXME: integrate
          MixpanelAnalyticsProvider(),
          FirebaseAnalyticsProvider(),
        ];
  late final Future<void> _initFuture;

  Map<String, Object> superprops = <String, Object>{};

  factory BitacoraAnalyticsLogger() =>
      inject(() => BitacoraAnalyticsLogger._());

  BitacoraAnalyticsLogger._() {
    _init();
  }

  Future<void> _init() async {
    _initFuture =
        Future.forEach<AnalyticsProvider>(_providers, (e) => e.init());
  }

  Future<void> _afterInit(Function(AnalyticsProvider) f) async {
    await _initFuture;

    _providers.forEach(f);
  }

  @override
  Future<void> setUserId(String? userId) async {
    return _afterInit((e) => e.setUserId(userId));
  }

  @override
  void registerSuperprops(Map<String, Object> props) {
    superprops.addAll(props);
  }

  @override
  void unregisterSuperprop(String name) {
    superprops.remove(name);
  }

  @override
  Future<void> logEvent(AnalyticsEvent event,
      {Map<String, Object> props = const {}}) async {
    final unitedProps = <String, Object>{};
    unitedProps.addAll(superprops);
    unitedProps.addAll(props);
    logger.i('analytics:event ${event.name} props:$unitedProps');
    return _afterInit((e) => e.logEvent(event.name, unitedProps));
  }

  @override
  NavigatorObserver get navigatorObserver => _MultiNavigatorObserver(_providers
      .map<NavigatorObserver>((e) => e.navigatorObserver)
      .toList(growable: false));
}

class _MultiNavigatorObserver extends NavigatorObserver {
  final List<NavigatorObserver> _observers;

  _MultiNavigatorObserver(this._observers);

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    _maybeLog('push', previousRoute, route);

    for (final observer in _observers) {
      observer.didPush(route, previousRoute);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    _maybeLog('pop', route, previousRoute);

    for (final observer in _observers) {
      observer.didPop(route, previousRoute);
    }
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didRemove(route, previousRoute);
    _maybeLog('remove', route, previousRoute);

    for (final observer in _observers) {
      observer.didRemove(route, previousRoute);
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    _maybeLog('replace', oldRoute, newRoute);

    for (final observer in _observers) {
      observer.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    }
  }

  @override
  void didStartUserGesture(
      Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didStartUserGesture(route, previousRoute);

    for (final observer in _observers) {
      observer.didStartUserGesture(route, previousRoute);
    }
  }

  @override
  void didStopUserGesture() {
    super.didStopUserGesture();

    for (final observer in _observers) {
      observer.didStopUserGesture();
    }
  }

  void _maybeLog(String action, Route? src, Route? dst) {
    if (_name(dst) == null || (src != null && _name(src) == null)) {
      return;
    }

    logger.i(
      'navigation:$action '
      '[${src == null ? '-> ' : '${_name(src)} -> '}'
      '${_name(dst)}]',
    );
  }

  String? _name(Route? route) {
    return route?.settings.name;
  }
}
