enum AnalyticsEvent {
  screenView('screen_view'),
  login('login'),
  signup('signup'),
  clickNewEntry('click_new_entry'),
  clickSaveEntry('click_save_entry'),
  clickSaveEntries('click_save_entries'),
  clickCreateExternalReport('click_create_external_report'),
  shareEntry('share_entry'),
  audioEntrySave('audio_entry_save'),
  audioEntryInterrupt('audio_entry_interrupt'),
  audioEntryCancel('audio_entry_cancel'),
  uploadFailed('upload_failed'),
  uploadSucceeded('upload_succeeded'),
  uploadAttachmentFailed('upload_attachment_failed'),
  uploadAttachmentSucceeded('upload_attachment_succeeded'),
  deleteEntry('delete_entry'),
  floatingActionMenuSelected('floating_action_menu_selected'),
  aiAudioEntrySelected('ai_audio_entry_selected'),
  aiVideoEntrySelected('ai_video_entry_selected'),
  bugReport('bug_report');

  final String name;

  const AnalyticsEvent(this.name);
}

const kAnalyticsPropUserId = 'user_id';
const kAnalyticsPropScreenName = 'screen_name';
const kAnalyticsPropLocalId = 'local_id';
const kAnalyticsPropRemoteId = 'remote_id';
const kAnalyticsPropLocalIds = 'local_ids';
const kAnalyticsPropRemoteIds = 'remote_ids';

const kAnalyticsPropIsRoutingToWebApp = 'is_routing_to_web_app';

const kAnalyticsPropUploadFailedStatusCode = 'status_code';

const kAnalyticsPropUploadModelType = 'model_type';

const kAnalyticsPropUploadAttachmentFailedError = 'error';
const kAnalyticsPropUploadAttachmentFailedS3 = 's3_key';

const kAnalyticsPropUploadAttachmentSucceededS3 = 's3_key';

const kAnalyticsPropUploadAttachmentTransferAttempts = 'transfer_attempts';

const kAnalyticsPropBugReportS3Key = 's3_key';
const kAnalyticsPropBugReportS3Url = 's3_url';
const kAnalyticsPropBugReportDescription = 'description';

const kAnalyticPropFloatingActionMenuSelectedOption =
    'floating_action_menu_selected_option';
const kAnalyticPropFloatingActionMenuSelectedAutoselect =
    'floating_action_menu_selected_autoselect';
