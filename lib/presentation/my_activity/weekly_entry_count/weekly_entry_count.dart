import 'dart:io';
import 'dart:math';

import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:bitacora/presentation/my_activity/my_activity_count_title.dart';
import 'package:bitacora/presentation/my_activity/weekly_entry_count/weekly_entry_count_repository_query.dart';
import 'package:bitacora/util/my_activity/my_activity_utils.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class WeeklyEntryCount extends StatefulWidget {
  const WeeklyEntryCount({super.key});

  @override
  State<WeeklyEntryCount> createState() => _WeeklyEntryCountState();
}

class _WeeklyEntryCountState extends State<WeeklyEntryCount> {
  List<int>? _values;
  double? _comparePercent;

  @override
  void initState() {
    super.initState();

    _loadData();
  }

  void _loadData() async {
    final db = context.read<Repository>();
    final user = context.read<ActiveSession>().value!.user;
    final organization = context.read<ActiveOrganization>().value!;

    final now = DateTime.now();
    final lastWeekDate = now.subtract(Duration(days: now.weekday + 1));
    final counts = await Future.wait([
      db.query(
        WeeklyEntryCountRepositoryQuery(now),
        context: db.context(
          queryScope: db.queryScope(
            userId: user.id,
            orgId: organization.id,
          ),
        ),
      ),
      db.query(
        WeeklyEntryCountRepositoryQuery(lastWeekDate),
        context: db.context(
          queryScope: db.queryScope(
            userId: user.id,
            orgId: organization.id,
          ),
        ),
      )
    ]);

    setState(() {
      _comparePercent =
          MyActivityUtils().calculateComparePercent(counts[0], counts[1]);
      _values = counts[0].values.toList(growable: false);
    });
  }

  double get _maxValue {
    final maxV = _values!.reduce(max).toDouble();
    return maxV > 10 ? maxV : 10;
  }

  @override
  Widget build(BuildContext context) {
    if (_values == null) {
      return Center(
        child: PlatformCircularProgressIndicator(),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MyActivityCountTitle(
          title: AppLocalizations.of(context)!.weekChartTitle,
          subtitle: AppLocalizations.of(context)!.weekChartSubtitle,
          comparePercent: _comparePercent!,
        ),
        SizedBox(height: 16.0),
        Expanded(child: BarChart(_buildBarChartData(context))),
      ],
    );
  }

  BarChartData _buildBarChartData(BuildContext context) {
    final barGroups = <BarChartGroupData>[];
    for (int i = 0; i < _values!.length; i++) {
      barGroups.add(BarChartGroupData(
        x: i,
        barRods: [
          BarChartRodData(
            toY: _values![i].toDouble(),
            width: 16,
            backDrawRodData: BackgroundBarChartRodData(
              show: true,
              toY: _maxValue,
              color: Colors.transparent,
            ),
            color: Theme.of(context).colorScheme.primary,
          ),
        ],
      ));
    }

    return BarChartData(
      alignment: BarChartAlignment.spaceBetween,
      barGroups: barGroups,
      titlesData: FlTitlesData(
        show: true,
        rightTitles:
            const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: _buildTitles,
            reservedSize: 32,
          ),
        ),
        leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      ),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false,
        horizontalInterval: _maxValue / 4.0,
      ),
      borderData: FlBorderData(show: false),
      barTouchData: BarTouchData(
        enabled: true,
        touchTooltipData: BarTouchTooltipData(
          tooltipRoundedRadius: 16.0,
          getTooltipColor: (_) {
            return Theme.of(context).colorScheme.surface;
          },
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            return BarTooltipItem(
              '${rod.toY.toInt()}',
              TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTitles(double value, TitleMeta meta) {
    const style = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 14,
    );
    final now = DateTime.now();
    final defaultLocale = Platform.localeName;
    final dateFormat = DateFormat('EEEE', defaultLocale);
    final day = dateFormat.format(
        now.subtract(Duration(days: now.weekday - value.toInt() - 1)))[0];

    return SideTitleWidget(
      meta: meta,
      child: Text(day.toUpperCase(), style: style),
    );
  }
}
