import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/presentation/login/login_page.dart';
import 'package:bitacora/presentation/onboarding/onboarding_view.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/presentation/widgets/page_view_dots_indicator/page_view_dots_indicator.dart';
import 'package:bitacora/presentation/widgets/theme_image.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

const kOnboardingPageMargin = kPageMargin * 1.5;

typedef OnboardingResultHandler = Function(LoginMode loginMode);

class OnboardingPage extends StatefulWidget {
  final OnboardingResultHandler resultHandler;

  const OnboardingPage({super.key, required this.resultHandler});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  late final List<OnboardingView> _views;
  final PageController _pageController = PageController(initialPage: 0);

  @override
  void initState() {
    super.initState();

    final localizations = AppLocalizationsResolver.get();
    _views = [
      OnboardingView(
        title: localizations.onboarding1Title,
        imagePath: 'images/onboarding/01.png',
      ),
      OnboardingView(
        title: localizations.onboarding2Title,
        subtitle: localizations.onboarding2Subtitle,
        imagePath: 'images/onboarding/02.png',
        hasTranslatedImage: true,
      ),
      OnboardingView(
        title: localizations.onboarding3Title,
        subtitle: localizations.onboarding3Subtitle,
        imagePath: 'images/onboarding/03.png',
      ),
      OnboardingView(
        title: localizations.onboarding4Title,
        imagePath: 'images/onboarding/06.png',
        boxFitImage: BoxFit.contain,
        hasTranslatedImage: true,
      ),
    ];
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: kOnboardingPageMargin),
        child: SafeArea(
          child: Column(
            children: [
              const _OnboardingAppLogo(),
              const SizedBox(height: 8.0),
              Flexible(
                child: PageView(
                  controller: _pageController,
                  physics: const ClampingScrollPhysics(),
                  children: _views,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0, top: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    PageViewDotsIndicator(
                      controller: _pageController,
                      pageCount: _views.length,
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: kOnboardingPageMargin,
                ),
                child: FilledButton(
                  onPressed: () async {
                    await _skipOnboarding();
                    widget.resultHandler(LoginMode.signup);
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.open_in_new),
                      const SizedBox(width: 8.0),
                      Text(AppLocalizationsResolver.get().signup),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: kOnboardingPageMargin,
                ),
                child: TextButton(
                  onPressed: () async {
                    await _skipOnboarding();
                    widget.resultHandler(LoginMode.login);
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(AppLocalizationsResolver.get().login),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _skipOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(SharedPreferencesKeys.onboardingWasSeen, true);
  }
}

class _OnboardingAppLogo extends StatelessWidget {
  const _OnboardingAppLogo();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: kOnboardingPageMargin, vertical: kPageMargin),
      child: Row(
        children: [
          const ThemeImage(
            name: 'images/ic_bitacora.png',
            height: 22.0,
          ),
          const SizedBox(width: 8.0),
          Text(
            AppConfig().appName,
            style: const TextStyle(
              fontSize: 18.0,
              fontWeight: FontWeight.bold,
              fontFamily: 'CerebriSans',
            ),
          ),
        ],
      ),
    );
  }
}
