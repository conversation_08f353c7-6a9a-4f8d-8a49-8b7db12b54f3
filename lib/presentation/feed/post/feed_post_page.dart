import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/feed_post/feed_post.dart';
import 'package:bitacora/domain/feed_post/value/feed_post_read_at.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:fwfh_url_launcher/fwfh_url_launcher.dart';
import 'package:provider/provider.dart';

class FeedPostPage extends StatefulWidget {
  final FeedPost post;

  const FeedPostPage({super.key, required this.post});

  @override
  State<FeedPostPage> createState() => _FeedPostPageState();
}

class _FeedPostPageState extends State<FeedPostPage> {
  @override
  void initState() {
    super.initState();

    _maybeMarkAsRead();
  }

  void _maybeMarkAsRead() async {
    if (widget.post.isRead) {
      return;
    }

    final db = context.read<Repository>();
    final activeOrg = context.read<ActiveOrganization>();

    await db.feedPost.markAsRead(
      db.context(),
      FeedPost(
        id: widget.post.id,
        organization: activeOrg.value!,
        readAt: FeedPostReadAt(DateTime.now()),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.post.title!.displayValue)),
      body: SingleChildScrollView(
        child: Padding(
          padding: kPageInsets.copyWith(bottom: 64),
          child: HtmlWidget(
            widget.post.content!.displayValue,
            factoryBuilder: () => WithUrlLauncherWidgetFactory(),
          ),
        ),
      ),
    );
  }
}

class WithUrlLauncherWidgetFactory extends WidgetFactory
    with UrlLauncherFactory {}
