import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/presentation/entry/filter/user/entry_filter_user_fields.dart';

class EntryFilterStaffRepositoryQuery extends RepositoryQuery<List<User>> {
  const EntryFilterStaffRepositoryQuery();

  @override
  Future<List<User>> run(RepositoryQueryContext context) =>
      context.db.user.searchStaff(context);

  @override
  Fields? fields(Repository db) => entryFilterUserFields(db);
}
