import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group/value/entry_group_created_at.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/presentation/entry/selection/entry_group_by_entry_id_repository_query.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/collection_selection/collection_selection.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class EntryGroupSelectionOption extends StatefulWidget {
  const EntryGroupSelectionOption({super.key});

  @override
  State<EntryGroupSelectionOption> createState() =>
      _EntryGroupSelectionOptionState();
}

class _EntryGroupSelectionOptionState extends State<EntryGroupSelectionOption> {
  final groupIds = <LocalId>{};
  bool isShowingButton = false;

  CollectionSelection<LocalId>? collectionSelection;

  @override
  void initState() {
    super.initState();

    collectionSelection = context.read<CollectionSelection<LocalId>>();
    collectionSelection!.addListener(_onSelectionChange);
    _onSelectionChange();
  }

  @override
  void dispose() {
    collectionSelection?.removeListener(_onSelectionChange);
    super.dispose();
  }

  void _onSelectionChange() async {
    final selections = context.read<CollectionSelection<LocalId>>().selections;

    if (selections.length < 2) {
      setState(() {
        isShowingButton = false;
      });
      return;
    }

    groupIds.clear();
    final db = context.read<Repository>();
    for (final entryId in selections) {
      final group =
          await db.query(EntryGroupByEntryIdRepositoryQuery(entryId: entryId));
      if (group != null) {
        groupIds.add(group.id!);

        if (groupIds.isNotEmpty) {
          setState(() {
            isShowingButton = false;
          });
          return;
        }
      }
    }

    if (groupIds.isEmpty) {
      setState(() {
        isShowingButton = true;
      });
      return;
    }

    setState(() {
      isShowingButton = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!isShowingButton) {
      return const SizedBox();
    }
    return IconButton(
      icon: Icon(Icons.add_link, color: Theme.of(context).colorScheme.primary),
      onPressed: () =>
          _linkSelections(context, collectionSelection!.selections),
    );
  }

  void _linkSelections(
    BuildContext context,
    Set<LocalId> selections,
  ) {
    context.read<CollectionSelection<LocalId>>().clear();
    final activeOrg = context.read<ActiveOrganization>().value!;

    final db = context.read<Repository>();

    db.entryGroup.save(
      db.context(),
      EntryGroup(
        name: EntryGroupName(''),
        entries: selections
            .map((e) => EntryGroupEntry(entry: Entry(id: e)))
            .toList(),
        organization: activeOrg,
        createdAt: EntryGroupCreatedAt(Clock().now()),
      ),
      requestSync: true,
    );
  }
}
