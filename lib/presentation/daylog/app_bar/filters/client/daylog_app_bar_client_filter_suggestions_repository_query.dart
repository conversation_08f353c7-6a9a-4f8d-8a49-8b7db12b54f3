import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';

class DaylogAppBarClientFilterSuggestionRepositoryQuery
    extends RepositoryQuery<List<CustomFieldMetadata>> {
  final LocalId customFieldId;

  DaylogAppBarClientFilterSuggestionRepositoryQuery({
    required this.customFieldId,
  });

  @override
  Future<List<CustomFieldMetadata>> run(RepositoryQueryContext context) {
    return context.db.customFieldMetadata
        .findValuesByCustomFieldId(context, customFieldId);
  }

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) {
    return db.customFieldMetadata.fieldsBuilder
        .value()
        .customField(db.customField.fieldsBuilder.build())
        .build();
  }
}
