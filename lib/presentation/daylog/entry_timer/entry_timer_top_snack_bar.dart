import 'dart:async';

import 'package:bitacora/application/entry_timer/entry_timer_handler.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/value/entry_timer_status.dart';
import 'package:bitacora/presentation/daylog/entry_timer/find_entry_with_timer_repository_query.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_navigator_props.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/widgets/timer/timer_widget.dart';
import 'package:bitacora/presentation/widgets/top_snack_bar.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class EntryTimerTopSnackbar extends StatefulWidget {
  const EntryTimerTopSnackbar({super.key});

  @override
  State<EntryTimerTopSnackbar> createState() => _EntryTimerTopSnackbarState();
}

class _EntryTimerTopSnackbarState extends State<EntryTimerTopSnackbar> {
  late final StreamSubscription? _subscription;
  final TimerWidgetController _timerController = TimerWidgetController();
  LocalId? _currentTimerEntryId;

  @override
  void initState() {
    super.initState();
    final db = context.read<Repository>();
    _subscription = db.entry.getMutations().listen(_onEntryMutation);
    _findEntryTimerInDb(db);
  }

  @override
  void dispose() {
    _subscription!.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_timerController.isStarted || _timerController.isTracking) {
      return const SizedBox();
    }
    return TopSnackBar(
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(AppLocalizations.of(context)!.entryTimer),
          TimerWidget(controller: _timerController),
          const SizedBox()
        ],
      ),
      color: bitacoraPurple,
      action: OutlinedButton(
        onPressed: () {
          _stopTimer();
        },
        child: Text(AppLocalizations.of(context)!.stop),
      ),
      onTap: () {
        EntryFormPage.navigate(
          EntryFormPageNavigatorProps(context, Entry(id: _currentTimerEntryId)),
        );
      },
    );
  }

  void _onEntryMutation(Mutation<Entry> mutation) {
    if (!mounted) {
      return;
    }

    final db = context.read<Repository>();
    final entry = mutation.model;

    switch (mutation.type.value) {
      case MutationTypeValue.insert:
        _maybeStartTimer(db, entry!.copyWith(id: mutation.id!));
        break;
      case MutationTypeValue.delete:
        if (mutation.id == _currentTimerEntryId) {
          _stopUiTimer();
        }
        break;
      case MutationTypeValue.unknown:
        if (_currentTimerEntryId == null) {
          _findEntryTimerInDb(db);
        } else {
          _maybeStopTimer(mutation.model);
        }
        break;
      case MutationTypeValue.update:
        if (mutation.id != _currentTimerEntryId) {
          return;
        }
        _maybeStopTimer(mutation.model);
        break;
    }
  }

  void _findEntryTimerInDb(Repository db) async {
    final entry =
        await db.query(const FindEntryWithStartedTimerRepositoryQuery());
    _maybeStartTimer(db, entry);
  }

  void _maybeStartTimer(Repository db, Entry? entry) async {
    if (entry == null ||
        entry.timerStatus != EntryTimerStatus.started ||
        entry.locationTracking != null) {
      return;
    }

    _currentTimerEntryId = entry.id;
    _startTimer(entry);
  }

  void _maybeStopTimer(Entry? entry) {
    if (entry == null) {
      return;
    }

    if (entry.timerStatus == EntryTimerStatus.finished) {
      _stopUiTimer();
      return;
    } else if ((entry.startDate != null && entry.startDate!.value == null) ||
        (entry.startTime != null && entry.startTime!.value == null)) {
      _deleteTimer();
    }
  }

  void _startTimer(Entry entry) {
    final elapsed = EntryTimerHandler().getCurrentElapsed(entry);
    setState(() {
      _timerController.maybeStart(initialElapsed: elapsed);
    });
  }

  void _stopUiTimer() {
    setState(() {
      _timerController.maybeEnd();
    });
  }

  void _stopTimer() {
    _stopUiTimer();
    EntryTimerHandler().stop(
      context.read<Repository>(),
      _currentTimerEntryId!,
    );
  }

  void _deleteTimer() {
    _stopUiTimer();
    EntryTimerHandler().delete(
      context.read<Repository>(),
      _currentTimerEntryId!,
    );
  }
}
