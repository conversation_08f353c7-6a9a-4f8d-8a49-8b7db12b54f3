import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/presentation/widgets/dots_loading_indicator.dart';
import 'package:bitacora/util/parent_builder/parent_builder.dart';
import 'package:bitacora/util/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SyncStateIndicator extends StatefulWidget {
  const SyncStateIndicator({super.key});

  @override
  State<SyncStateIndicator> createState() => _SyncStateIndicatorState();
}

class _SyncStateIndicatorState extends State<SyncStateIndicator> {
  final ValueNotifier<DotsLoadingIndicatorStatus> _animationState =
      ValueNotifier(DotsLoadingIndicatorStatus.idle);
  late SyncState _syncState;

  @override
  void initState() {
    super.initState();
    _syncState = context.read<SyncState>();
    _syncState.addListener(_onUpdateSyncState);
    _onUpdateSyncState();
  }

  void _onUpdateSyncState() async {
    if (!_syncState.isBusy) {
      if (_syncState.lastResult == SyncResult.connectionError &&
          _syncState.wasTriggeredByUser()) {
        Toast().showNetworkErrorToast();
      }
    }

    setState(() {});
  }

  @override
  void dispose() {
    _syncState.removeListener(_onUpdateSyncState);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final activeSession = context.watch<ActiveSession>();

    if (!activeSession.value!.isValid) {
      _animationState.value = DotsLoadingIndicatorStatus.error;
    } else if (_syncState.isBusy) {
      _animationState.value = DotsLoadingIndicatorStatus.busy;
    } else if (_syncState.hasPendingUploads) {
      _animationState.value = DotsLoadingIndicatorStatus.incomplete;
    } else {
      _animationState.value = DotsLoadingIndicatorStatus.idle;
    }

    return ParentBuilder(
      builder: (context, child) {
        if (_animationState.value == DotsLoadingIndicatorStatus.incomplete) {
          return Badge(
            offset: const Offset(8, -4),
            label: Text('${_syncState.nPendingUploads}'),
            child: child,
          );
        }

        return child;
      },
      child: DotsLoadingIndicator(state: _animationState),
    );
  }
}
