import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/application/openai/openai_service.dart';
import 'package:bitacora/application/openai/worklog_audio_handler.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_source/entry_source.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_api_translator.dart';
import 'package:bitacora/infrastructure/worklog/worklog_api_translator.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/daylog_ai_option_button.dart';
import 'package:bitacora/presentation/daylog/audio/simple_audio_recorder.dart';
import 'package:bitacora/presentation/daylog/audio/simple_audio_recorder_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_navigator_props.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/daylog_ai_processing_button.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/file_converter/file_converter_utils.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/location_utils.dart';
import 'package:bitacora/util/toast/flutter_toast_utils.dart';
import 'package:file/local.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

const _kAiButtonWidth = 200.0;
const _kAiButtonHeight = 48.0;
const _kAiOptionDistance = 56.0;
const _kAiAnimationDuration = 300;
const _kAiScaleStart = 0.8;
const _kAiScaleEnd = 1.0;
const _kAiCompletedAnimationDuration = 1000;

enum ProcessingState { idle, processing, completed }

class AiFloatingActionButton extends StatefulWidget {
  const AiFloatingActionButton({
    super.key,
  });

  @override
  State<AiFloatingActionButton> createState() => _AiFloatingActionButtonState();
}

class _AiFloatingActionButtonState extends State<AiFloatingActionButton>
    with TickerProviderStateMixin {
  SimpleAudioRecorderController? _audioRecorderController;
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isExpanded = false;
  WorklogAudioHandler? _worklogAudioHandler;

  final ValueNotifier<ProcessingState> _processingState =
      ValueNotifier(ProcessingState.idle);

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: _kAiAnimationDuration),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  WorklogAudioHandler get worklogAudioHandler {
    if (_worklogAudioHandler == null) {
      final projectCache = context.read<ProjectCache>();
      final activeOrg = context.read<ActiveOrganization>();
      _worklogAudioHandler =
          WorklogAudioHandler(projectCache, OpenAiService(), activeOrg);
    }
    return _worklogAudioHandler!;
  }

  @override
  void dispose() {
    _animationController.dispose();
    _processingState.dispose();
    _audioRecorderController?.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    if (_processingState.value == ProcessingState.processing) {
      return;
    }

    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: _kAiButtonWidth,
      height: _kAiButtonHeight,
      child: Stack(
        alignment: Alignment.bottomRight,
        clipBehavior: Clip.none,
        children: [
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Positioned(
                right: _kAiOptionDistance +
                    4.0 +
                    _kAiOptionDistance * _animation.value,
                bottom: 0,
                child: Opacity(
                  opacity: _animation.value,
                  child: Transform.scale(
                    scale: _kAiScaleStart +
                        (_kAiScaleEnd - _kAiScaleStart) * _animation.value,
                    child: DaylogAiOptionButton(
                      icon: Icons.video_camera_back,
                      color: bitacoraBlue,
                      onPressed: () => _handleVideoOption(context),
                    ),
                  ),
                ),
              );
            },
          ),
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Positioned(
                right: _kAiOptionDistance + 4.0,
                bottom: 0,
                child: Opacity(
                  opacity: _animation.value,
                  child: Transform.scale(
                    scale: _kAiScaleStart +
                        (_kAiScaleEnd - _kAiScaleStart) * _animation.value,
                    child: DaylogAiOptionButton(
                      icon: Icons.mic,
                      color: bitacoraGreen,
                      onPressed: () => _handleAudioOption(context),
                    ),
                  ),
                ),
              );
            },
          ),
          Positioned(
            right: 4.0,
            child: ValueListenableBuilder<ProcessingState>(
              valueListenable: _processingState,
              builder: (context, state, _) {
                return state != ProcessingState.idle
                    ? DaylogAiProcessingButton(state: state)
                    : FloatingActionButton(
                        heroTag: 'fab-ai',
                        onPressed: _toggleExpanded,
                        mini: true,
                        backgroundColor: _isExpanded
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context)
                                .colorScheme
                                .surfaceContainerHighest,
                        child: Icon(
                          Icons.auto_awesome,
                          color: _isExpanded
                              ? Theme.of(context).colorScheme.onPrimary
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      );
              },
            ),
          ),
          if (_audioRecorderController != null)
            Positioned(
              right: _kAiOptionDistance + 4.0,
              bottom: 0,
              child: SizedBox(
                height: 48.0,
                child: SimpleAudioRecorder(
                  controller: _audioRecorderController!,
                  onCancel: _handleAudioRecordingCancel,
                  onSave: _handleAudioRecordingSave,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _startProcessing() {
    setState(() {
      _isExpanded = false;
      _animationController.reverse();
    });

    _processingState.value = ProcessingState.processing;
  }

  void _completeProcessing() {
    _processingState.value = ProcessingState.completed;

    Future.delayed(const Duration(milliseconds: _kAiCompletedAnimationDuration),
        () {
      if (mounted) {
        _processingState.value = ProcessingState.idle;
      }
    });
  }

  void _handleAudioOption(BuildContext context) async {
    unawaited(context
        .read<AnalyticsLogger>()
        .logEvent(AnalyticsEvent.aiAudioEntrySelected));

    _toggleExpanded();

    _startProcessing();

    _audioRecorderController = SimpleAudioRecorderController();

    final cleanupListener = _setupCompletionListener(worklogAudioHandler);

    try {
      await _audioRecorderController!.startRecording();
    } catch (e) {
      if (_processingState.value == ProcessingState.processing) {
        _processingState.value = ProcessingState.idle;
      }

      _audioRecorderController?.dispose();
      _audioRecorderController = null;

      cleanupListener();

      if (mounted) {
        FluttertoastUtils().showToast(msg: e.toString());
      }
    }
  }

  void _handleAudioRecordingCancel() async {
    await _audioRecorderController?.cancelRecording();
    _audioRecorderController?.dispose();
    _audioRecorderController = null;

    if (_processingState.value == ProcessingState.processing) {
      _processingState.value = ProcessingState.idle;
    }
  }

  void _handleAudioRecordingSave() async {
    if (_audioRecorderController == null) return;

    final activeLogDay = context.read<ActiveLogDay>();
    final activeSession = context.read<ActiveSession>();
    final contextSnapshot = EntryFormPageContextSnapshot(context);

    try {
      await _audioRecorderController!.stopRecording();
      final audioFile = await _audioRecorderController!.recordingFile!;

      final worklogData =
          await worklogAudioHandler.processAudioToWorklog(audioFile);

      final attachment = await _audioRecorderController!
          .processRecordingToAttachment(audioFile);

      _audioRecorderController?.dispose();
      _audioRecorderController = null;

      final position = await LocationUtils().determinePosition();

      final entry = _createEntry(
        activeLogDay: activeLogDay,
        activeSession: activeSession,
        worklogData: worklogData,
        attachment: attachment,
        position: position.toLatLng(),
      );

      _completeProcessing();

      await EntryFormPage.navigate(
        EntryFormPageNavigatorProps.fromContextSnapshot(contextSnapshot, entry),
      );
    } catch (e) {
      logger.e(e);
      if (_processingState.value == ProcessingState.processing) {
        _processingState.value = ProcessingState.idle;
      }

      _audioRecorderController?.dispose();
      _audioRecorderController = null;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(AppLocalizations.of(context)!
                  .errorProcessingAudio(e.toString()))),
        );
      }
    }
  }

  void _handleVideoOption(BuildContext context) async {
    final FileSystem fs = FileSystemInjector.get();
    final activeLogDay = context.read<ActiveLogDay>();
    final activeSession = context.read<ActiveSession>();
    final contextSnapshot = EntryFormPageContextSnapshot(context);

    unawaited(
      context
          .read<AnalyticsLogger>()
          .logEvent(AnalyticsEvent.aiVideoEntrySelected),
    );

    _toggleExpanded();

    final videoFile = await ImagePicker().pickVideo(source: ImageSource.camera);

    if (videoFile == null) {
      return;
    }

    _startProcessing();

    final cleanupListener = _setupCompletionListener(worklogAudioHandler);

    try {
      final audioPath = await FileConverterUtils().videoToAudio(videoFile.path);
      final audioFile = LocalFileSystem().file(audioPath);

      final worklogData =
          await worklogAudioHandler.processAudioToWorklog(audioFile);

      final position = await LocationUtils().determinePosition();
      final processedRelativePath =
          await AttachmentUtils().processForSave(fs.file(videoFile.path));
      final attachment = Attachment(
          s3Key: AttachmentS3Key(const Uuid().v4()),
          name: AttachmentName(path.basename(processedRelativePath)),
          isUploaded: AttachmentIsUploaded(false),
          isDownloaded: AttachmentIsDownloaded(true),
          transferState:
              AttachmentTransferStateValueObject(AttachmentTransferState.na),
          path: AttachmentPath(processedRelativePath));

      final entry = _createEntry(
        activeLogDay: activeLogDay,
        activeSession: activeSession,
        worklogData: worklogData,
        attachment: attachment,
        position: position.toLatLng(),
      );

      _completeProcessing();

      await EntryFormPage.navigate(
        EntryFormPageNavigatorProps.fromContextSnapshot(contextSnapshot, entry),
      );
    } catch (e) {
      logger.e(e);
      if (_processingState.value == ProcessingState.processing) {
        _processingState.value = ProcessingState.idle;
      }

      cleanupListener();

      if (mounted) {
        FluttertoastUtils().showToast(
            msg: AppLocalizationsResolver.get()
                .errorProcessingVideo(e.toString()));
      }
    }
  }

  Entry _createEntry({
    required ActiveLogDay activeLogDay,
    required ActiveSession activeSession,
    required Map<String, dynamic> worklogData,
    required Attachment attachment,
    required LatLng position,
  }) {
    final now = Clock().now();

    return Entry(
        day: activeLogDay.value,
        time: LogTime.fromDateTime(now),
        author: activeSession.value!.user,
        attachments: [],
        tags: [],
        location: LatLngValueObject(position),
        comments: EntryComments(worklogData['comments'] ?? ''),
        extension: WorklogApiTranslator().fromMap(worklogData['extension']),
        source: EntrySource(
          type: EntrySourceType.mobileAi,
          transcription:
              EntrySourceTranscription(worklogData['source']['transcription']),
          attachment: attachment,
          metadata: EntrySourceMetadata.fromMap(
            {'transcription': worklogData['source']['transcription']},
          ),
        ),
        metadata: EntryMetadataApiTranslator()
            .fromMetadataMap(worklogData['metadata']));
  }

  Function _setupCompletionListener(WorklogAudioHandler worklogAudioHandler) {
    late void Function() listener;
    bool listenerAdded = false;

    listener = () {
      if (worklogAudioHandler.isCompleted.value) {
        _completeProcessing();
        worklogAudioHandler.isCompleted.removeListener(listener);
        listenerAdded = false;
      }
    };
    worklogAudioHandler.isCompleted.addListener(listener);
    listenerAdded = true;

    return () {
      if (listenerAdded) {
        worklogAudioHandler.isCompleted.removeListener(listener);
        listenerAdded = false;
      }
    };
  }
}
