import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/util/limit_offset_cursor.dart';

class MyFirstReportRepositoryQuery extends RepositoryQuery<List<Report>> {
  const MyFirstReportRepositoryQuery();

  @override
  Future<List<Report>> run(RepositoryQueryContext context) =>
      context.db.report.findAllDownloaded(
          context.copyWith(cursor: const LimitOffsetCursor(2, 0)));

  @override
  Fields fields(Repository db) => db.report.fieldsBuilder.path().build();
}
