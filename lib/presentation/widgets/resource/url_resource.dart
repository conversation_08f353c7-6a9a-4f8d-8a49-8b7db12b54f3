import 'package:bitacora/domain/common/live_model.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/util/url_launcher.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class UrlResource extends StatelessWidget {
  final LiveModel<Resource> liveResource;

  const UrlResource({super.key, required this.liveResource});

  @override
  Widget build(BuildContext context) {
    final resource = liveResource.value;
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.15),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16.0),
        onTap: _open,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.link),
            const SizedBox(width: 8.0),
            Text(resource!.name!.value),
          ],
        ),
      ),
    );
  }

  void _open() async {
    final url = liveResource.value!.path!.value!;
    final urlLauncher = UrlLauncher();
    if (await urlLauncher.canLaunch(url)) {
      await urlLauncher.launch(url, mode: LaunchMode.externalApplication);
    }
  }
}
