import 'dart:io';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/entry_timer/entry_timer_handler.dart';
import 'package:bitacora/application/location_tracking/entry/location_tracking_owner_strategy_entry.dart';
import 'package:bitacora/application/location_tracking/location_tracking_handler.dart';
import 'package:bitacora/application/router.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_controller.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_presenter.dart';
import 'package:bitacora/presentation/location_tracking/tracking_max_duration_option.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/presentation/widgets/floating_action_menu/floating_action_menu_arc_text.dart';
import 'package:bitacora/presentation/widgets/floating_action_menu/floating_action_menu_secondary_item.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/bitacora_icons.dart';
import 'package:bitacora/util/toast/flutter_toast_utils.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

const kAutoSelectDefaultSecondaryOptionDuration = Duration(milliseconds: 1500);
const _kShowDuration = Duration(milliseconds: 400);
const _kHideDuration = Duration(milliseconds: 100);

class FloatingActionMenu extends StatefulWidget {
  final VoidCallback primaryAction;
  final Function(bool isShowing)? onIsShowingChanged;

  const FloatingActionMenu({
    super.key,
    required this.primaryAction,
    this.onIsShowingChanged,
  });

  @override
  State<FloatingActionMenu> createState() => _FloatingActionMenuState();
}

class _FloatingActionMenuState extends State<FloatingActionMenu>
    with TickerProviderStateMixin {
  final GlobalKey _key = GlobalKey();
  final ValueNotifier<Key?> _secondarySelectedKey = ValueNotifier(null);
  final ValueNotifier<bool> _isShowingMenu = ValueNotifier(false);

  late final List<FloatingActionMenuSecondaryItem> _secondaryItems;
  late final AnimationController _transitionAnimationController;
  late final Animation<double> _transitionAnimation;
  final AudioEntryListenerPresenterController audioListenerPresenterController =
      AudioEntryListenerPresenterController();

  bool _isAutoSelectEnabled = true;
  bool _isAutoSelected = false;
  bool? _isShowingArcText;

  @override
  void initState() {
    super.initState();
    if (widget.onIsShowingChanged != null) {
      _isShowingMenu.addListener(_onShowingMenuChanged);
    }

    _secondaryItems = [
      FloatingActionMenuSecondaryItem(
        key: const ValueKey<String>('chronometer'),
        icon: Icons.timer_outlined,
        action: () {
          final db = context.read<Repository>();
          final activeLogDay = context.read<ActiveLogDay>();
          final activeOrganization = context.read<ActiveOrganization>();
          final activeSession = context.read<ActiveSession>();
          final activeProject = context.read<ActiveProject>();
          EntryTimerHandler()
              .start(
                db,
                activeLogDay,
                activeOrganization,
                activeSession,
                activeProject,
                Localizations.localeOf(context).languageCode,
                AppLocalizations.of(context)!,
              )
              .catchError((_) => FluttertoastUtils().showToast(
                  msg: AppLocalizationsResolver.get().moreThanOneTimer));
        },
      ),
      FloatingActionMenuSecondaryItem(
        key: const ValueKey<String>('entry_location_tracking'),
        icon: Icons.route,
        action: () {
          LocationTrackingMaxDurationOption.show(
            context: context,
            title: AppLocalizations.of(context)!.trackerEntry,
            onStart: () {
              const LocationTrackingHandler().start(
                context,
                LocationTrackingOwnerStrategyEntry(),
              );
            },
          );
        },
      ),
      FloatingActionMenuSecondaryItem(
        key: const ValueKey<String>('qr'),
        icon: Icons.qr_code,
        action: () => Navigator.of(context).pushNamed(kRouteQrScan),
      ),
      FloatingActionMenuSecondaryItem(
        key: const ValueKey<String>('audio'),
        icon: Icons.mic,
        action: () async {
          var sttEnabled = AppConfig().isSpeechToTextInAudioRecorderEnabled;
          if (sttEnabled) {
            final prefs = await SharedPreferences.getInstance();
            sttEnabled = sttEnabled &&
                (prefs.getBool(SharedPreferencesKeys
                        .isAudioRecordingSpeechToTextEnabled) ??
                    true);
          }

          audioListenerPresenterController.show(
            Platform.isAndroid || !sttEnabled
                ? AudioEntryListenerMode.onlyRecord
                : AudioEntryListenerMode.all,
            isAnchored: true,
          );
        },
      ),
      FloatingActionMenuSecondaryItem(
        key: const ValueKey<String>('stt'),
        icon: BitacoraIcons.graphic_eq,
        action: () => audioListenerPresenterController.show(
          AudioEntryListenerMode.onlyStt,
          isAnchored: true,
        ),
      ),
    ];
    _transitionAnimationController = AnimationController(
      vsync: this,
      duration: _kShowDuration,
      reverseDuration: _kHideDuration,
    );
    _transitionAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _transitionAnimationController,
        curve: Curves.elasticInOut,
        reverseCurve: Curves.ease,
      ),
    );

    _transitionAnimationController.addListener(() {
      if (_transitionAnimationController.isDismissed) {
        _isShowingMenu.value = false;
      }
    });
    _loadSharedPreferences();
  }

  void _loadSharedPreferences() async {
    const key = SharedPreferencesKeys.showFloatingActionMenuLongPressArcText;
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isShowingArcText = prefs.getBool(key);
    });
  }

  @override
  void dispose() {
    _secondarySelectedKey.dispose();
    _transitionAnimationController.dispose();
    _isShowingMenu.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
        valueListenable: _isShowingMenu,
        builder: (context, isShowingMenu, _) {
          final theme = Theme.of(context).brightness == Brightness.light
              ? buildDarkThemeData(bitacoraGreen)
              : buildLightThemeData(bitacoraGreen);
          return Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (isShowingMenu)
                ScaleTransition(
                  scale: _transitionAnimation,
                  child: Column(
                    children: [
                      const SizedBox(height: 4.0),
                      Theme(
                        data: theme,
                        child: Container(
                          padding: const EdgeInsets.all(8.0),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(32.0),
                          ),
                          child: Row(
                            key: _key,
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: _secondaryItems.map((e) {
                              final showCircularProgressBar =
                                  e.key.value == 'stt' && _isAutoSelectEnabled;
                              return e.render(
                                _secondarySelectedKey,
                                showCircularProgressBar:
                                    showCircularProgressBar,
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: 8.0),
              GestureDetector(
                onLongPressStart: (details) {
                  _maybeDisableArcText();

                  Future.delayed(
                    kAutoSelectDefaultSecondaryOptionDuration,
                    () => _maybeAutoSelect(details),
                  );

                  _showMenu();
                },
                onLongPressMoveUpdate: (details) {
                  if (_isAutoSelected) {
                    audioListenerPresenterController.longPressPosition.value =
                        details.localPosition;
                    return;
                  }

                  _detectTapedItem(
                      details.globalPosition, details.localPosition);
                },
                onLongPressEnd: (details) {
                  if (!_isAutoSelected) {
                    final secondarySelected = _getSelected();
                    if (secondarySelected != null) {
                      _analyticLogEvent(secondarySelected);

                      secondarySelected.action();
                    }
                  }

                  _hide();
                },
                child: AudioEntryListenerPresenter(
                  controller: audioListenerPresenterController,
                  child: Stack(
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          FloatingActionButton(
                            onPressed: () async {
                              widget.primaryAction();

                              if (_isShowingArcText != null) {
                                return;
                              }

                              const key = SharedPreferencesKeys
                                  .showFloatingActionMenuLongPressArcText;
                              final prefs =
                                  await SharedPreferences.getInstance();

                              await prefs.setBool(key, true);
                              Future.delayed(const Duration(milliseconds: 200),
                                  () {
                                setState(() {
                                  _isShowingArcText = true;
                                });
                              });
                            },
                            backgroundColor:
                                Theme.of(context).colorScheme.onSurface,
                            child: const Icon(Icons.add),
                          ),
                        ],
                      ),
                      if (_isShowingArcText ?? false)
                        Builder(builder: (context) {
                          final text = AppLocalizations.of(context)!.longPress;
                          return Positioned(
                            bottom: 28,
                            right: 28,
                            child: FloatingActionMenuArcText(
                              radius: 32,
                              text: text.toUpperCase().replaceAll(' ', '   '),
                              textStyle: const TextStyle(
                                color: bitacoraRed,
                                fontWeight: FontWeight.w900,
                                fontSize: 12,
                                letterSpacing: -1.5,
                              ),
                            ),
                          );
                        }),
                    ],
                  ),
                ),
              ),
            ],
          );
        });
  }

  void _onShowingMenuChanged() {
    widget.onIsShowingChanged!(_isShowingMenu.value);
  }

  void _detectTapedItem(Offset globalPosition, Offset localPosition) {
    final box =
        _key.currentContext?.findAncestorRenderObjectOfType<RenderBox>();
    if (box == null) {
      return;
    }

    final local = box.globalToLocal(globalPosition);
    if (localPosition.dy < -100.0 || localPosition.dy > 70.0 || local.dx < 0) {
      _selectIndex(null);
      return;
    }

    final result = BoxHitTestResult();
    if (box.hitTest(result, position: Offset(local.dx, 30.0))) {
      for (final hit in result.path) {
        final target = hit.target;
        if (target is FloatingActionMenuSecondaryItemProxyBox &&
            _secondarySelectedKey.value != target.key) {
          _selectIndex(target.key!);
        }
      }
    }
  }

  void _maybeDisableArcText() async {
    final needsSetState = _isShowingArcText ?? false;
    _isShowingArcText = false;
    if (needsSetState) {
      setState(() {});
    }

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(
      SharedPreferencesKeys.showFloatingActionMenuLongPressArcText,
      false,
    );
  }

  void _selectIndex(Key? index) {
    if (index == null && _secondarySelectedKey.value == null) {
      return;
    }

    _secondarySelectedKey.value = index;
    HapticFeedback.lightImpact();

    if (_secondarySelectedKey.value != _secondaryItems.last.key) {
      setState(() {
        _isAutoSelectEnabled = false;
      });
    }
  }

  void _maybeAutoSelect(LongPressStartDetails details) {
    if (!_isAutoSelectEnabled || _secondaryItems.length < 2) {
      return;
    }

    if (_getSelected()?.key == _secondaryItems.last.key &&
        _transitionAnimationController.isCompleted) {
      _analyticLogEvent(_secondaryItems.last, isAutoselect: true);

      _isAutoSelected = true;
      audioListenerPresenterController.longPressPosition.value =
          details.localPosition;
      audioListenerPresenterController.show(
        AudioEntryListenerMode.onlyStt,
      );

      final ticker =
          _transitionAnimationController.animateTo(0, duration: Duration.zero);
      ticker.whenComplete(() => _isShowingMenu.value = false);

      HapticFeedback.lightImpact();
    }
  }

  void _showMenu() {
    _isShowingMenu.value = true;
    _transitionAnimationController.forward();
    _selectIndex(_secondaryItems.last.key);
  }

  void _hide() {
    setState(() {
      audioListenerPresenterController.longPressPosition.value = null;
      _transitionAnimationController.reverse();
      _isAutoSelected = false;
      _isAutoSelectEnabled = true;
    });
  }

  FloatingActionMenuSecondaryItem? _getSelected() {
    return _secondaryItems
        .firstWhereOrNull((e) => e.key == _secondarySelectedKey.value);
  }

  void _analyticLogEvent(
    FloatingActionMenuSecondaryItem item, {
    bool isAutoselect = false,
  }) {
    context.read<AnalyticsLogger>().logEvent(
      AnalyticsEvent.floatingActionMenuSelected,
      props: {
        kAnalyticPropFloatingActionMenuSelectedOption: item.key.value,
        if (isAutoselect) kAnalyticPropFloatingActionMenuSelectedAutoselect: 1,
      },
    );
  }
}
