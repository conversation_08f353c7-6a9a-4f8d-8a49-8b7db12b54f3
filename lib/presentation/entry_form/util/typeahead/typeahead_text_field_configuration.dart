import 'package:bitacora/util/parent_builder/parent_builder.dart';
import 'package:flutter/material.dart';

class TypeaheadTextFieldConfiguration {
  final FocusNode focusNode;
  final TextEditingController controller;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final TextCapitalization textCapitalization;
  final TextAlignVertical? textAlignVertical;
  final bool autofocus;
  final InputDecoration? decoration;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onSubmitted;
  final EdgeInsets scrollPadding;
  final ValueChanged<String>? onChanged;
  final FormFieldValidator<String>? validator;

  const TypeaheadTextFieldConfiguration({
    required this.controller,
    required this.focusNode,
    this.keyboardType,
    this.textInputAction,
    this.textCapitalization = TextCapitalization.none,
    this.textAlignVertical,
    this.autofocus = false,
    this.scrollPadding = const EdgeInsets.all(20.0),
    this.decoration,
    this.onEditingComplete,
    this.onSubmitted,
    this.onChanged,
    this.validator,
  });

  TypeaheadTextFieldConfiguration copyWith({
    FocusNode? focusNode,
    TextEditingController? controller,
    EdgeInsets? scrollPadding,
    ValueChanged<String>? onSubmitted,
    InputDecoration? decoration,
  }) {
    return TypeaheadTextFieldConfiguration(
      controller: controller ?? this.controller,
      focusNode: focusNode ?? this.focusNode,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      textCapitalization: textCapitalization,
      textAlignVertical: textAlignVertical,
      autofocus: autofocus,
      scrollPadding: scrollPadding ?? this.scrollPadding,
      decoration: decoration ?? this.decoration,
      onEditingComplete: onEditingComplete,
      onSubmitted: onSubmitted ?? this.onSubmitted,
      onChanged: onChanged,
      validator: validator,
    );
  }

  Widget builder(
    BuildContext context,
    TextEditingController controller,
    FocusNode focusNode, [
    LayerLink? layerLink,
  ]) {
    return ParentBuilder(
      builder: (context, child) {
        if (layerLink == null) {
          return child;
        }

        return CompositedTransformTarget(
          link: layerLink,
          child: child,
        );
      },
      child: TextFormField(
        focusNode: focusNode,
        controller: controller,
        scrollPadding: scrollPadding,
        keyboardType: keyboardType,
        textInputAction: textInputAction,
        textCapitalization: textCapitalization,
        textAlignVertical: textAlignVertical,
        decoration: decoration,
        onFieldSubmitted: onSubmitted,
        autofocus: autofocus,
        onEditingComplete: onEditingComplete,
        onChanged: onChanged,
        validator: validator,
      ),
    );
  }
}
