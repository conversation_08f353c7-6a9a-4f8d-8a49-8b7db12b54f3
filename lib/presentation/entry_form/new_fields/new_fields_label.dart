import 'package:bitacora/presentation/entry_form/new_fields/new_fields_notifier.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

const _bottomPadding = 10.0;
const _topPadding = 4.0;

class NewFieldsLabel extends StatelessWidget {
  const NewFieldsLabel({super.key});

  @override
  Widget build(BuildContext context) {
    final newFieldsNotifier = context.watch<NewFieldsNotifier>();
    if (!newFieldsNotifier.hasNewFields()) {
      return const SizedBox(height: _bottomPadding);
    }

    final themeData = Theme.of(context);
    final color = getFieldMarkedAsNewColor(context);

    return Padding(
      padding: const EdgeInsets.only(top: _topPadding, bottom: _bottomPadding),
      child: Row(
        children: [
          Icon(
            Icons.vignette,
            color: color,
            size: 10,
          ),
          const SizedBox(width: 3),
          Text(
            AppLocalizations.of(context)!.newEntries,
            style: themeData.textTheme.bodySmall!.copyWith(color: color),
          ),
        ],
      ),
    );
  }
}
