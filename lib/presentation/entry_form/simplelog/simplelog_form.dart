import 'package:bitacora/presentation/entry_form/simplelog/simplelog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/newable_field_suggestion_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:bitacora/presentation/entry_form/worklog/worklog_title_suggestion_repository_query.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class SimplelogForm extends StatefulWidget {
  final SimplelogFormController controller;

  const SimplelogForm({super.key, required this.controller});

  @override
  State<SimplelogForm> createState() => _SimplelogFormState();
}

class _SimplelogFormState extends State<SimplelogForm> {
  final Key _titleKey = GlobalKey();
  final FocusNode _titleFocus = FocusNode();
  final FocusNode _commentsFocus = FocusNode();

  @override
  void dispose() {
    _titleFocus.dispose();
    _commentsFocus.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _SimplelogFormSkeleton(
      title: NewableFieldSuggestionTypeahead(
        key: _titleKey,
        suggestionsQuery: const WorklogTitleSuggestionRepositoryQuery(),
        queryScope: getOrgScope(context),
        onSelect: (_) => maybeRequestFocus(
          _commentsFocus,
          widget.controller.comments.text,
          false,
        ),
        textFieldConfiguration: TypeaheadTextFieldConfiguration(
          focusNode: _titleFocus,
          controller: widget.controller.title,
          textInputAction: TextInputAction.next,
          textCapitalization: TextCapitalization.sentences,
          decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.formTitle),
          onSubmitted: (value) => maybeRequestFocus(
            _commentsFocus,
            widget.controller.comments.text,
            true,
          ),
          validator: getRequiredFieldValidator(),
        ),
      ),
      comments: getCommentsRow(
        context: context,
        focusNode: _commentsFocus,
        controller: widget.controller,
      ),
    );
  }
}

class NonEditableSimplelogForm extends StatelessWidget {
  final SimplelogFormController controller;

  const NonEditableSimplelogForm({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      child: Theme(
        data: Theme.of(context).copyWith(
          inputDecorationTheme: const InputDecorationTheme(
            contentPadding: EdgeInsets.only(),
            border: InputBorder.none,
            isDense: true,
          ),
        ),
        child: _SimplelogFormSkeleton(
          isEditable: false,
          title: TextFormField(
            controller: controller.title,
            decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.formTitle),
          ),
          comments: controller.comments.text.isEmpty
              ? null
              : TextFormField(
                  controller: controller.comments,
                  decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.formComments),
                ),
        ),
      ),
    );
  }
}

class _SimplelogFormSkeleton extends StatelessWidget {
  final Widget title;
  final Widget? comments;
  final bool isEditable;

  const _SimplelogFormSkeleton({
    required this.title,
    required this.comments,
    this.isEditable = true,
  });

  @override
  Widget build(BuildContext context) {
    final children = [
      title,
      if (comments != null) ...[
        const SizedBox(height: kFormVerticalSpacing),
        comments!,
      ]
    ];

    return isEditable
        ? SliverList(delegate: SliverChildListDelegate.fixed(children))
        : Column(children: children);
  }
}
