import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/infrastructure/custom_field/custom_field_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/organization/organization_db_contract.dart';
import 'package:bitacora/util/inject/inject.dart';

class CustomFieldSuggestionEngine {
  factory CustomFieldSuggestionEngine() =>
      inject(() => const CustomFieldSuggestionEngine._());

  const CustomFieldSuggestionEngine._();

  Future<List<String>> suggestion(
    DbContext context,
    LocalId customFieldId,
  ) async {
    final executor = await context.executor;
    const metadata = CustomFieldMetadataDbContract();
    const customField = CustomFieldDbContract();
    const organization = OrganizationDbContract();
    final results = await executor.rawQuery(
      '''
        SELECT ${metadata.value} 
        FROM ${metadata.tableName}
        LEFT JOIN ${customField.tableName}
          ON ${metadata.customFieldId} = ${customField.id}
        LEFT JOIN ${organization.tableName}
          ON ${customField.organizationId} = ${organization.id}
        WHERE ${customField.id} = ? 
          AND ${organization.id} = ?
          AND ${metadata.value} LIKE ?
        ORDER BY ${metadata.updatedAt} DESC, 
          ${metadata.value}  ASC
        LIMIT ${context.cursor?.limit ?? 10}
        OFFSET ${context.cursor?.offset ?? 0}
      ''',
      [
        customFieldId.dbValue,
        context.queryScope!.orgId!.dbValue,
        context.queryScope!.pattern,
      ],
    );

    return results
        .map<String>((e) => e[metadata.value] as String)
        .toList(growable: false);
  }
}
