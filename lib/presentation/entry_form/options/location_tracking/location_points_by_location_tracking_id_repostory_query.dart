import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/location_point/location_point.dart';

class LocationPointsByLocationTrackingRepositoryQuery
    extends RepositoryQuery<List<LocationPoint>> {
  final LocalId locationTrackingId;

  const LocationPointsByLocationTrackingRepositoryQuery(
      this.locationTrackingId);

  @override
  Future<List<LocationPoint>> run(RepositoryQueryContext context) =>
      context.db.locationPoint.findAll(context, locationTrackingId);

  @override
  Fields fields(Repository db) =>
      db.locationPoint.fieldsBuilder.latLong().speed().createdAt().build();
}
