import 'package:bitacora/application/router.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/signature/signature_s3_downloader.dart';
import 'package:bitacora/domain/common/live_model.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/presentation/entry_form/options/signature/entry_signature_item_repository_query.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';

class EntrySignatureItem extends StatefulWidget {
  final Signature signature;

  const EntrySignatureItem({super.key, required this.signature});

  @override
  State<EntrySignatureItem> createState() => _SignatureItemState();
}

class _SignatureItemState extends State<EntrySignatureItem> {
  late final LiveModel<Signature> _liveSignature;
  bool _isDownloading = false;

  @override
  void initState() {
    super.initState();
    final db = context.read<Repository>();
    _liveSignature = LiveModel<Signature>(
      widget.signature,
      db.signature.getMutations(),
      () => db.query(
          EntrySignatureItemRepositoryQuery(signatureId: widget.signature.id!)),
    );

    if (_liveSignature.value!.doodle?.value == null) {
      _downloadDoodle();
    }
  }

  Future<void> _downloadDoodle() async {
    final db = context.read<Repository>();
    setState(() {
      logger.i('isDownloading true');
      _isDownloading = true;
    });

    try {
      final doodle = await SignatureS3Downloader()
          .download(widget.signature.s3Key!.value!);

      await db.signature.save(db.context(),
          Signature(id: widget.signature.id, doodle: SignatureDoodle(doodle)));
    } catch (e) {
      setState(() {
        _isDownloading = false;
      });
    }

    setState(() {
      _isDownloading = false;
    });
  }

  @override
  void dispose() {
    _liveSignature.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.of(context)
            .pushNamed(kRouteSignatureForm, arguments: _liveSignature.value);
      },
      child: ValueListenableBuilder<Signature?>(
        valueListenable: _liveSignature,
        builder: (context, signature, _) {
          if (signature == null) {
            return const SizedBox();
          }

          return SizedBox(
            width:
                kSignatureDoodleSize.width * (64 / kSignatureDoodleSize.height),
            child: IntrinsicWidth(
              child: Column(
                children: [
                  Container(
                    constraints: const BoxConstraints(maxHeight: 64),
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(8.0),
                          topRight: Radius.circular(8.0)),
                    ),
                    child: AspectRatio(
                      aspectRatio: kSignatureDoodleSize.aspectRatio,
                      child: _isDownloading
                          ? Center(child: PlatformCircularProgressIndicator())
                          : signature.doodle?.value == null
                              ? Center(
                                  child: Icon(
                                  Icons.cloud_outlined,
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                ))
                              : SvgPicture.string(
                                  signature.doodle!.value!,
                                  colorFilter: ColorFilter.mode(
                                    Theme.of(context).colorScheme.onSurface,
                                    BlendMode.srcIn,
                                  ),
                                ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Divider(
                      color: Theme.of(context).colorScheme.onSurface,
                      height: 1,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Column(
                      children: [
                        Text(
                          signature.ownerName!.displayValue,
                          style: Theme.of(context).textTheme.bodySmall,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4.0),
                        Text(signature.status!.displayValue,
                            style: Theme.of(context).textTheme.labelSmall),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
