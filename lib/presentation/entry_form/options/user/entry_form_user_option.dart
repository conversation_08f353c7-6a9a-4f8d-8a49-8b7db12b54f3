import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/presentation/entry_form/options/user/assignee_suggestion_repository_query.dart';
import 'package:bitacora/presentation/entry_form/options/user/entry_form_user_option_controller.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/newable_field_suggestion_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class EntryFormUserOption extends StatefulWidget {
  final EntryFormUserOptionController controller;

  const EntryFormUserOption({
    super.key,
    required this.controller,
  });

  @override
  State<EntryFormUserOption> createState() => _EntryFormUserOptionState();
}

class _EntryFormUserOptionState extends State<EntryFormUserOption> {
  final GlobalKey _assigneeKey = GlobalKey();
  final FocusNode _assigneeFocus = FocusNode();

  @override
  void dispose() {
    _assigneeFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: kFormVerticalSpacing,
        horizontal: kFormHorizontalSpacing,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${AppLocalizations.of(context)!.creator} | ',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Flexible(
                child: Text(
                  _getAuthor(context),
                  style: Theme.of(context)
                      .textTheme
                      .bodySmall!
                      .copyWith(fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
          const SizedBox(height: kFormVerticalSpacing),
          NewableFieldSuggestionTypeahead(
            key: _assigneeKey,
            suggestionsQuery: const AssigneeSuggestionRepositoryQuery(),
            queryScope: getOrgScope(context),
            textFieldConfiguration: TypeaheadTextFieldConfiguration(
              focusNode: _assigneeFocus,
              controller: widget.controller.assigneeTextEditingController,
              textInputAction: TextInputAction.done,
              textCapitalization: TextCapitalization.sentences,
              decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.assignedTo),
            ),
          ),
        ],
      ),
    );
  }

  String _getAuthor(BuildContext context) {
    return widget.controller.liveEntry.value?.author?.name?.displayValue ??
        context.read<ActiveSession>().value?.user.name?.displayValue ??
        '';
  }
}
