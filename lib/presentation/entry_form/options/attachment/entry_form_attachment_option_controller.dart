import 'dart:async';

import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/attachment/edit_attachment.dart';
import 'package:bitacora/domain/common/edit/edit_collection.dart';
import 'package:bitacora/domain/common/edit/edit_model.dart';
import 'package:bitacora/domain/common/live_model.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/attachment/file_util.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/attachment_ui_repository_query.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/entry_form_attachment_option.dart';
import 'package:bitacora/presentation/entry_form/options/entry_form_option_controller.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/util/app_settings/app_settings_utils.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/collection_selection/collection_selection.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';
import 'package:bitacora/util/device_info_plugin.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/form/form_has_changes.dart';
import 'package:bitacora/util/platform_utils.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:bitacora/util/take_picture/take_picture_page.dart';
import 'package:collection/collection.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

const _kImagePickerPrefixIos = 'image_picker_';
const _kImagePickerPrefixAndroid = 'image_picker';
const _kIsProcessingPicksDelay = Duration(milliseconds: 100);

enum PickIntention { camera, photo, video, file }

class EntryFormAttachmentOptionController extends EntryFormOptionController
    with HasChanges {
  final FileSystem _fileSystem = FileSystemInjector.get();
  final Repository db;

  final ValueKey<String> stagingKey =
      ValueKey('${DateTime.now().microsecondsSinceEpoch}');
  final EditCollection<Attachment> attachments =
      EditCollection([], (e) => EditAttachment(e));
  final ValueNotifier<bool> isProcessingPicks = ValueNotifier(false);
  final ValueNotifier<bool> isPermissionDenied = ValueNotifier(false);
  final CollectionSelection<AttachmentS3Key> selection = CollectionSelection();

  PickIntention? permissionDeniedPickIntention;
  bool _isDisposed = false;

  EntryFormAttachmentOptionController(
    ContextSnapshot contextSnapshot,
    ValueNotifier<Entry?> liveEntry,
  )   : db = contextSnapshot.read<Repository>(),
        super(liveEntry) {
    liveEntry.addListener(_read);
    _read();
    if (hasData.value) {
      isShowing.value = true;
    }
  }

  void _read() {
    if (liveEntry.value == null) {
      return;
    }
    attachments.updateListFromDb(
      liveEntry.value!.attachments!.map(
        (a) => LiveModel<Attachment>(
          a,
          db.attachment.getMutations(),
          () => db.query(AttachmentUiRepositoryQuery(attachmentId: a.id!)),
        ),
      ),
    );

    _readFromSource();

    hasData.value = attachments.value.isNotEmpty;
  }

  void _readFromSource() {
    if (liveEntry.value?.source?.attachment != null) {
      attachments.insert([liveEntry.value!.source!.attachment!]);
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    liveEntry.removeListener(_read);
    attachments.dispose();
    isProcessingPicks.dispose();
    selection.dispose();
    super.dispose();
  }

  void cleanStagingDirectory() {
    StorageUtils()
        .getStagingDirectory(stagingKey)
        .then((directory) => directory.delete(recursive: true));
  }

  @override
  MaterialColor get color => bitacoraBlue;

  @override
  IconData get icon => Icons.attach_file;

  @override
  Widget form(BuildContext context) => EntryFormAttachmentOption(
      controller: this, maybeNotifyChanges: _maybeNotifyChanges);

  Future<void> pickFiles(FilePicker filePicker) {
    return _pickFromFilePicker(filePicker);
  }

  Future<void> pickPhotoGallery(ImagePicker imagePicker) {
    return _pickFromImagePicker(imagePicker);
  }

  Future<void> pickVideoGallery(ImagePicker imagePicker) {
    return _pickFromImagePicker(imagePicker, PickIntention.video);
  }

  Future<void> _pickFromFilePicker(FilePicker filePicker) async {
    isPermissionDenied.value = false;
    Future.delayed(
        _kIsProcessingPicksDelay, () => isProcessingPicks.value = true);
    try {
      final result = await filePicker.pickFiles(
        type: FileType.any,
        allowMultiple: true,
      );
      if (result == null) {
        isProcessingPicks.value = false;
        return;
      }

      final pickedFiles =
          result.paths.map((path) => _fileSystem.file(path!)).toList();
      await _maybeAddPickedFiles(pickedFiles);
    } on PlatformException catch (e) {
      if (e.code == 'read_external_storage_denied') {
        return Future.delayed(_kIsProcessingPicksDelay, () {
          isPermissionDenied.value = true;
          isProcessingPicks.value = false;
          permissionDeniedPickIntention = PickIntention.file;
        });
      }
    }
  }

  Future<void> _pickFromImagePicker(
    ImagePicker imagePicker, [
    PickIntention pickIntention = PickIntention.photo,
  ]) async {
    isPermissionDenied.value = false;

    Future.delayed(
      _kIsProcessingPicksDelay,
      () => isProcessingPicks.value = true,
    );

    try {
      List<XFile>? results;
      if (pickIntention == PickIntention.video) {
        final result = await imagePicker.pickVideo(source: ImageSource.gallery);
        if (result != null) {
          results = <XFile>[result];
        }
      } else {
        if (await _isMultiImageAllowed()) {
          results = await imagePicker.pickMultiImage();
        } else {
          final result =
              await imagePicker.pickImage(source: ImageSource.gallery);
          if (result != null) {
            results = <XFile>[result];
          }
        }
      }

      if (results?.isEmpty ?? true) {
        isProcessingPicks.value = false;
        return;
      }

      final pickedFiles = results!.map((result) {
        var basename = path.basename(result.path);

        /// iOS must be processed first due to
        /// _kImagePickerPrefixIos.startsWith(_kImagePickerPrefixAndroid) == true
        if (basename.startsWith(_kImagePickerPrefixIos)) {
          basename = basename.replaceFirst(_kImagePickerPrefixIos, '');
        } else if (basename.startsWith(_kImagePickerPrefixAndroid)) {
          basename = basename.replaceFirst(_kImagePickerPrefixAndroid, '');
        }

        final filepath = path.join(path.dirname(result.path), basename);
        return _fileSystem.file(result.path).renameSync(filepath);
      }).toList();
      await _maybeAddPickedFiles(pickedFiles);
    } on PlatformException catch (e) {
      if (e.code == 'photo_access_denied') {
        return Future.delayed(_kIsProcessingPicksDelay, () {
          isPermissionDenied.value = true;
          isProcessingPicks.value = false;
          permissionDeniedPickIntention = pickIntention;
        });
      }
    }
  }

  Future<void> pickCamera(BuildContext context) async {
    Future.delayed(const Duration(milliseconds: 100),
        () => isProcessingPicks.value = true);
    final result = await takePicture(context);
    if (result == null) {
      isProcessingPicks.value = false;
      return;
    }

    await _maybeAddPickedFiles([_fileSystem.file(result.path)]);
  }

  Future<void> _maybeAddPickedFiles(List<File> pickedFiles) async {
    if (_isDisposed) {
      return;
    }
    final editAttachments = attachments.value.toList();
    final toInsert = <Attachment>[];
    for (final file in pickedFiles) {
      final processedFile =
          await AttachmentUtils().processForStaging(file, stagingKey);
      if (_isDisposed) {
        return;
      }
      var wasAlreadySelected = false;
      for (final editAttachment in editAttachments) {
        final attachment = editAttachment.value!;
        if (attachment.path!.value == processedFile.path) {
          wasAlreadySelected = true;
          break;
        }
      }
      if (!wasAlreadySelected) {
        toInsert.add(Attachment(
          // Note: System may override s3Key/transferDetails if s3Key/path
          // is already in db when saving the attachment.
          s3Key: AttachmentS3Key(const Uuid().v4()),
          name: AttachmentName(path.basename(processedFile.path)),
          isUploaded: AttachmentIsUploaded(false),
          isDownloaded: AttachmentIsDownloaded(true),
          transferState:
              AttachmentTransferStateValueObject(AttachmentTransferState.na),
          path: AttachmentPath(processedFile.path, false),
        ));
      }
    }

    isProcessingPicks.value = false;
    attachments.insert(toInsert);
    _maybeNotifyChanges();
    hasData.value = attachments.value.isNotEmpty;
  }

  Future<void> deleteSelection() async {
    final editAttachments = attachments.value.toList();
    final toDelete = <EditModel<Attachment>>[];
    for (var i = editAttachments.length - 1; i >= 0; i--) {
      final attachment = editAttachments[i].value!;
      if (selection.isSelected(attachment.s3Key!)) {
        final filePath = attachment.path!.value;
        if (filePath != null &&
            await StorageUtils().isInStaging(filePath, stagingKey)) {
          await maybeDeleteFile(_fileSystem.file(filePath));
        }
        toDelete.add(editAttachments[i]);
      }
    }

    attachments.delete(toDelete);
    _maybeNotifyChanges();
    hasData.value = attachments.value.isNotEmpty;
    selection.clear();
  }

  void clearSelection() {
    selection.clear();
  }

  Future<bool> _isMultiImageAllowed() async {
    if (PlatformUtils().isIOS) {
      final iosInfo = await DeviceInfoPluginInjector.get().iosInfo;
      final systemVersion = int.parse(iosInfo.systemVersion.split(('.')).first);
      return systemVersion >= 14;
    }

    return true;
  }

  void openAppSettings() {
    isPermissionDenied.value = false;
    AppSettingsUtils().openAppSettings();
  }

  void _maybeNotifyChanges() {
    final currentAttachments =
        attachments.value.map((e) => e.value).toList(growable: true);

    if (currentAttachments.length !=
        (liveEntry.value?.attachments?.length ?? 0)) {
      hasChanges.value = true;
      return;
    }

    if (liveEntry.value?.attachments?.isNotEmpty ?? true) {
      for (final currentAttachment in currentAttachments) {
        final found = liveEntry.value?.attachments!.firstWhereOrNull(
            (e) => e.s3Key?.value == currentAttachment!.s3Key!.value);

        if (found == null) {
          hasChanges.value = true;
          return;
        }

        if (currentAttachment!.comments?.value != found.comments?.value ||
            currentAttachment.doodle != found.doodle) {
          hasChanges.value = true;
          return;
        }
      }
    } else if (currentAttachments.isNotEmpty) {
      hasChanges.value = true;
      return;
    }

    hasChanges.value = false;
  }
}
