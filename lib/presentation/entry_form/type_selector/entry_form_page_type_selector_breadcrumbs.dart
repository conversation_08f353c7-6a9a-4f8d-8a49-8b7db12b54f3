import 'package:bitacora/util/parent_builder/parent_builder.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

class EntryFormPageTypeSelectorBreadcrumbs extends StatefulWidget {
  final String path;
  final Function(String) onDirectoryTap;

  const EntryFormPageTypeSelectorBreadcrumbs({
    super.key,
    required this.path,
    required this.onDirectoryTap,
  });

  @override
  State<EntryFormPageTypeSelectorBreadcrumbs> createState() =>
      _EntryFormPageTypeSelectorBreadcrumbsState();
}

class _EntryFormPageTypeSelectorBreadcrumbsState
    extends State<EntryFormPageTypeSelectorBreadcrumbs> {
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final textButtonStyle = TextButton.styleFrom(
      padding: const EdgeInsets.symmetric(horizontal: 2.0),
      minimumSize: const Size(50, 30),
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      alignment: Alignment.center,
    );

    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.positions.last.maxScrollExtent,
          duration: const Duration(milliseconds: 600),
          curve: Curves.ease,
        );
      }
    });

    final directories = widget.path.split('/');
    return SingleChildScrollView(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          TextButton(
            onPressed: () {
              widget.onDirectoryTap('/');
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 2.0),
              minimumSize: const Size(20, 30),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              alignment: Alignment.center,
            ),

            child: Icon(
              Icons.home,
              size: 20,
              color: primaryColor,
            ),
          ),
          const SizedBox(width: 2.0),
          const Text('/'),
          const SizedBox(width: 2.0),
          ...directories.expandIndexed((i, directoryName) sync* {
            final isLast = i == directories.length - 1;
            yield ParentBuilder(
              builder: (context, child) {
                return isLast
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 2.0),
                        child: child,
                      )
                    : TextButton(
                        style: textButtonStyle,
                        child: child,
                        onPressed: () {
                          widget.onDirectoryTap(
                              directories.slice(0, i + 1).join('/'));
                        },
                      );
              },
              child: Text(
                directoryName,
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    color: isLast
                        ? Theme.of(context).colorScheme.onSurface
                        : primaryColor),
              ),
            );

            if (!isLast) {
              yield const SizedBox(width: 2.0);
              yield const Text('/');
              yield const SizedBox(width: 2.0);
            }
          }),
          const SizedBox(width: 8.0),
        ].toList(),
      ),
    );
  }
}
