import 'package:bitacora/domain/extension/extension_type.dart';

extension ApiExtensionType on ExtensionType {
  static ExtensionType fromApiString(String s) {
    if (s == 'Worklog') {
      return ExtensionType.worklog;
    } else if (s == 'Inventorylog') {
      return ExtensionType.inventorylog;
    } else if (s == 'Personnellog') {
      return ExtensionType.personnellog;
    } else if (s == 'Progresslog') {
      return ExtensionType.progresslog;
    } else if (s == 'Templatelog') {
      return ExtensionType.templatelog;
    }
    throw ArgumentError('Unexpected extension type api string <$s>');
  }
}
