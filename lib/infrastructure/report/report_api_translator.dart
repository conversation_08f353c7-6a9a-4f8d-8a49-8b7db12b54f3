import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/util/date_utils.dart';

class ReportApiTranslator implements ModelTranslator<Report> {
  const ReportApiTranslator();

  @override
  Report fromMap(Map<String, dynamic> data) {
    return Report(
      uuid: ReportUuid(data['uuid']),
      createdAt: ReportCreatedAt(getDateTimeFromApi(data['created_at'])),
    );
  }

  @override
  Map<String, dynamic> toMap(Report model) {
    throw UnimplementedError();
  }
}
