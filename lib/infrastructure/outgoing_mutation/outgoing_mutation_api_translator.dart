import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model_type.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/infrastructure/api_translator.dart';

class OutgoingMutationApiTranslator
    implements ModelTranslator<OutgoingMutation> {
  final ApiTranslator apiTranslator;

  const OutgoingMutationApiTranslator(this.apiTranslator);

  @override
  OutgoingMutation fromMap(Map<String, dynamic> data) {
    throw UnimplementedError();
  }

  @override
  Map<String, dynamic> toMap(OutgoingMutation mutation) {
    final model = mutation.model!;
    if (mutation.mutationType == MutationType.delete) {
      switch (model.type) {
        case OutgoingMutationModelType.entryGroupEntry:
        case OutgoingMutationModelType.entryGroup:
          return {};
        default:
          return {
            'id': model.value.remoteId!.apiValue,
            'delete': true,
          };
      }
    }

    switch (model.type) {
      case OutgoingMutationModelType.worklog:
      case OutgoingMutationModelType.inventorylog:
      case OutgoingMutationModelType.personnellog:
      case OutgoingMutationModelType.progresslog:
      case OutgoingMutationModelType.templatelog:
        return apiTranslator.entry.toMap(model.value as Entry);
      case OutgoingMutationModelType.locationTracking:
        return apiTranslator.locationTracking
            .toMap(model.value as LocationTracking);
      case OutgoingMutationModelType.signature:
        return apiTranslator.signature.toMap(model.value as Signature);
      case OutgoingMutationModelType.entryGroup:
        return apiTranslator.entryGroup.toMap(model.value as EntryGroup);
      case OutgoingMutationModelType.entryGroupEntry:
        return apiTranslator.entryGroupEntry
            .toMap(model.value as EntryGroupEntry);
      default:
        throw 'Unsupported model to api <model>';
    }
  }
}
