import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class UserLocationTrackingDbContract extends DbContract {
  static const String _ = 'ult_';
  static const String _tableName = 'userLocationTracking';

  final String userId = '${_}userId';
  final String locationTrackingId = '${_}locationTrackingId';
  final String organizationId = '${_}organizationId';

  const UserLocationTrackingDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithUserLocationTracking;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $userId INTEGER NOT NULL,
    $locationTrackingId INTEGER NOT NULL,
    $organizationId INTEGER NOT NULL,
    PRIMARY KEY ($userId, $locationTrackingId, $organizationId)
  )
  ''';
}
