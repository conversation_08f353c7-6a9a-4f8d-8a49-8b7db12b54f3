import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_contract.dart';

class EntryMetadataDbFieldsBuilder extends DbFieldsBuilder
    implements EntryMetadataFieldsBuilder {
  EntryMetadataDbFieldsBuilder() {
    _id();
  }

  EntryMetadataDbContract get contract => const EntryMetadataDbContract();

  EntryMetadataDbFieldsBuilder _id() {
    addField(
      EntryMetadataField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  EntryMetadataDbFieldsBuilder remoteId() {
    addField(
      EntryMetadataField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  EntryMetadataDbFieldsBuilder value() {
    addField(
      EntryMetadataField.value,
      DbField(
        column: contract.value,
        valueBuilder: (v) => EntryMetadataValue(v),
      ),
    );
    return this;
  }

  @override
  EntryMetadataDbFieldsBuilder type() {
    addField(
      EntryMetadataField.type,
      DbField(
        column: contract.type,
        valueBuilder: (v) => EntryMetadataType.fromDbValue(v),
      ),
    );
    return this;
  }



  @override
  EntryMetadataDbFieldsBuilder entry(Fields fields) {
    addField(
      EntryMetadataField.entry,
      DbField(
        key: EntryMetadataField.entry,
        column: contract.entryId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.entry.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
