import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/location_point/location_point.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/location_point/location_point_db_contract.dart';

class LocationPointDbTranslator implements DbTranslator<LocationPoint> {
  const LocationPointDbTranslator();

  @override
  Set<Field> get nestedModelFields => locationPointNestedModelFields;

  @override
  Future<LocationPoint> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return LocationPoint(
      id: fields[LocationPointField.id]?.value(map),
      remoteId: fields[LocationPointField.remoteId]?.value(map),
      latLong: fields[LocationPointField.latLong]?.value(map),
      speed: fields[LocationPointField.speed]?.value(map),
      tracking: await fields[LocationPointField.tracking]?.nested(context, map),
      createdAt: fields[LocationPointField.createdAt]?.value(map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
    DbContext context,
    LocationPoint model,
  ) async {
    final map = <String, dynamic>{};
    const contract = LocationPointDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);

    if (model.latLong != null) {
      map[contract.latitude] = model.latLong!.value?.latitude;
      map[contract.longitude] = model.latLong!.value?.longitude;
    }

    addField(map, contract.speed, model.speed);

    await saveNestedModel<LocationTracking>(
      context,
      map,
      contract.trackingId,
      context.db.locationTracking,
      model.tracking,
    );

    addField(map, contract.createdAt, model.createdAt);
    return map;
  }
}
