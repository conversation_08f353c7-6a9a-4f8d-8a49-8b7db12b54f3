import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/location_point/location_point.dart';
import 'package:bitacora/domain/location_point/location_point_repository.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/location_point/location_point_db_contract.dart';
import 'package:bitacora/infrastructure/location_point/location_point_db_fields_builder.dart';
import 'package:bitacora/infrastructure/location_point/location_point_db_translator.dart';

class LocationPointDbTable
    extends DbTable<LocationPoint, LocationPointDbFieldsBuilder>
    implements
        LocationPointRepository<DbContext, LocationPointDbFieldsBuilder> {
  final DbTranslator<LocationPoint> _translator;

  LocationPointDbTable([this._translator = const LocationPointDbTranslator()]);

  @override
  LocationPointDbContract get contract => const LocationPointDbContract();

  @override
  DbTranslator<LocationPoint> get translator => _translator;

  @override
  LocationPointDbFieldsBuilder get fieldsBuilder =>
      LocationPointDbFieldsBuilder();

  @override
  Future<LocationPoint?> findLast(DbContext context) {
    return takeFirst(
        query(context, limit: 1, orderBy: '${contract.createdAt} DESC'));
  }

  @override
  Future<List<LocationPoint>> findAll(
    DbContext context,
    LocalId locationTrackingId,
  ) {
    return query(context,
        where: '${contract.trackingId} = ?',
        whereArgs: [locationTrackingId.dbValue],
        orderBy: '${contract.createdAt} ASC');
  }

  @override
  Future<List<LocationPoint>> findNotSynced(
    DbContext context,
    LocalId locationTrackingId,
  ) {
    return query(
      context,
      where: '${contract.remoteId} IS NULL AND ${contract.trackingId} = ?',
      whereArgs: [locationTrackingId.dbValue],
      limit: context.cursor?.limit,
    );
  }

  @override
  Future<void> deleteAll(DbContext context, LocalId locationTrackingId) async {
    final points = await findAll(
      context.copyWith(fields: context.db.locationPoint.fieldsBuilder.build()),
      locationTrackingId,
    );

    for (final point in points) {
      await delete(context, point.id!);
    }
  }
}
