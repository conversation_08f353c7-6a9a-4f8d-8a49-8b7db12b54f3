import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:flutter/foundation.dart';

class DbFields implements Fields {
  final Map<Field, DbField> map;

  DbFields(this.map);

  @override
  Set<Field> get keys => map.keys.toSet();
}

abstract class DbFieldsBuilder {
  final DbFields _fields = DbFields(<Field, DbField>{});
  bool _hasBuilt = false;

  @protected
  void addField(Field key, DbField field) {
    if (_fields.map[key] != null) {
      return;
    }
    _fields.map[key] = field;
  }

  DbFields build() {
    if (_hasBuilt) {
      throw 'DbFieldsBuilder was reused. That\'s a no no.';
    }
    _hasBuilt = true;
    return _fields;
  }
}
