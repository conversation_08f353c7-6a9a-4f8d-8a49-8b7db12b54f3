import 'dart:async';

import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class TemplateGroupDbContract extends DbContract {
  static const String _ = 'tg_';
  static const String _tableName = 'templateGroup';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String name = '${_}name';
  final String order = '${_}order';
  final String templateId = '${_}templateId';

  const TemplateGroupDbContract() : super(_, _tableName);

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $name TEXT,
    $order INTEGER NOT NULL,
    $templateId INTEGER NOT NULL
  )
  ''';

  @override
  int get initialDbVersion => kDbVersionWithTemplatelogTables;

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < kDbVersionWithTemplatelogTables) {
      return db.execute(create);
    }

    if (oldVersion < kDbVersionWithTemplateGroupNameNullable) {
      await db.execute('ALTER TABLE $tableName RENAME TO ${tableName}_old');
      await db.execute(create);
      await db.execute('INSERT INTO $tableName SELECT * FROM ${tableName}_old');
      await db.execute('DROP TABLE ${tableName}_old');
    }
  }
}
