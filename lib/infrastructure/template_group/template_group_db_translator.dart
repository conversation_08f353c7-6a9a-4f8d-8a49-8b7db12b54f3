import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/template_group/template_group.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/template_group/template_group_db_contract.dart';

class TemplateGroupDbTranslator implements DbTranslator<TemplateGroup> {
  const TemplateGroupDbTranslator();

  @override
  Set<TemplateGroupField> get nestedModelFields =>
      templateGroupNestedModelFields;

  @override
  Future<TemplateGroup> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return TemplateGroup(
      id: fields[TemplateGroupField.id]?.value(map),
      remoteId: fields[TemplateGroupField.remoteId]?.value(map),
      name: fields[TemplateGroupField.name]?.value(map),
      order: fields[TemplateGroupField.order]?.value(map),
      conditions:
          await fields[TemplateGroupField.conditions]?.nested(context, map),
      template: await fields[TemplateGroupField.template]?.nested(context, map),
      blocks: await fields[TemplateGroupField.blocks]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, TemplateGroup model) async {
    final map = <String, dynamic>{};
    const contract = TemplateGroupDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.name, model.name);
    addField(map, contract.order, model.order);

    await saveNestedModel<Template>(
        context, map, contract.templateId, context.db.template, model.template);
    return map;
  }
}
