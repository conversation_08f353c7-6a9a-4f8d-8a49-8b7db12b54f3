import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/open_state/open_state.dart';

class OpenStateApiTranslator implements ModelTranslator<OpenState> {
  const OpenStateApiTranslator();

  @override
  OpenState fromMap(Map<String, dynamic> data) {
    // Note: OpenState db schema doesn't require startDay. It is inferred from
    // entry.day.
    return OpenState(
      progress: OpenStateProgress(data['progress'] ?? 0),
      progressive: OpenStateProgressive(data['progressive']),
      endDay: OpenStateEndDay(data['end_day']),
    );
  }

  @override
  Map<String, dynamic> toMap(OpenState model) {
    return {
      'progressive': model.progressive!.apiValue,
      'end_day': model.endDay!.apiValue,
    };
  }
}
