import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class TagDbContract extends DbContract {
  static const String _ = 't_';
  static const String _tableName = 'tag';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String name = '${_}name';
  final String color = '${_}color';
  final String organizationId = '${_}organizationId';

  const TagDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersion;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $name TEXT NOT NULL,
    $color INTEGER NOT NULL,
    $organizationId INTEGER NOT NULL
  )
  ''';
}
