import 'dart:ui';

import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';

class TagApiTranslator implements ModelTranslator<Tag> {
  const TagApiTranslator();

  @override
  Tag fromMap(Map<String, dynamic> data) {
    final name = data['name'];
    final color = data['color'];

    return Tag(
      remoteId: RemoteId(data['id']),
      name: Tag<PERSON>ame(name),
      color: TagColor(color != null ? Color(color) : getTagColor(name)),
      organization: Organization(remoteId: RemoteId(data['organization_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(Tag model) {
    final map = <String, dynamic>{};
    if (model.remoteId!.apiValue != null) {
      map['id'] = model.remoteId!.apiValue;
    }
    map['name'] = model.name!.apiValue;
    return map;
  }
}
