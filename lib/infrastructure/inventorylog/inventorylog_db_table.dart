import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/domain/inventorylog/inventorylog_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/extension/extension_db_table.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_contract.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_fields_builder.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_translator.dart';

class InventorylogDbTable
    extends ExtensionDbTable<Inventorylog, InventorylogDbFieldsBuilder>
    implements InventorylogRepository<DbContext, InventorylogDbFieldsBuilder> {
  @override
  InventorylogDbContract get contract => const InventorylogDbContract();

  @override
  DbTranslator<Inventorylog> get translator => const InventorylogDbTranslator();

  @override
  InventorylogDbFieldsBuilder get fieldsBuilder =>
      InventorylogDbFieldsBuilder();

  @override
  ExtensionType get extensionType => ExtensionType.inventorylog;

  @override
  Future<List<String>> itemNames(DbContext context) {
    return suggestion(context, contract.itemName);
  }

  @override
  Future<List<String>> providers(DbContext context) {
    return suggestion(context, contract.provider);
  }

  @override
  Future<List<String>> reasons(DbContext context) {
    return suggestion(context, contract.reason);
  }
}
