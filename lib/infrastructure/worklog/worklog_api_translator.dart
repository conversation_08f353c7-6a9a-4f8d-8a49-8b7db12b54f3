import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/worklog/worklog.dart';
import 'package:bitacora/infrastructure/project/project_api_translator.dart';

class WorklogApiTranslator implements ModelTranslator<Worklog> {
  const WorklogApiTranslator();

  @override
  Worklog fromMap(Map<String, dynamic> data) {
    return Worklog(
      remoteId: RemoteId(data['id']),
      title: WorklogTitle(data['title']),
      quantity: WorklogQuantity(data['quantity']),
      sublocation: WorklogSublocation(data['sublocation']),
      costPrice: WorklogCostPrice(data['cost_price']),
      salePrice: WorklogSalePrice(data['sale_price']),
      paymentStatus: WorklogPaymentStatus.fromDbValue(data['payment_status']),
      priceIsUnit: WorklogPriceIsUnit(data['price_is_unit'] == 1),
      provider: WorklogProvider(data['provider']),
      type: WorklogType.fromDbValue(data['worklog_type']),
      project: Project(
        remoteId: RemoteId(data['project_id']),
        name: ProjectName(data['project_name']),
        organization: Organization(remoteId: RemoteId(data['organization_id'])),
      ),
    );
  }

  @override
  Map<String, dynamic> toMap(Worklog model) {
    final map = {
      'title': model.title!.apiValue,
      'quantity': model.quantity!.apiValue,
      'sublocation': model.sublocation!.apiValue,
      'cost_price': model.costPrice!.apiValue,
      'sale_price': model.salePrice!.apiValue,
      'payment_status': model.paymentStatus!.apiValue,
      'price_is_unit': model.priceIsUnit!.apiValue,
      'provider': model.provider!.apiValue,
      'worklog_type': model.type!.apiValue,
    };
    map.addAll(const ProjectApiTranslator()
        .toMapWithPrefix(model.project!, prefix: 'project_'));
    return map;
  }
}
