import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/organization/organization_db_contract.dart';

class OrganizationDbTranslator implements DbTranslator<Organization> {
  const OrganizationDbTranslator();

  @override
  Set<Field> get nestedModelFields => organizationNestedModelFields;

  @override
  Future<Organization> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Organization(
      id: fields[OrganizationField.id]?.value(map),
      remoteId: fields[OrganizationField.remoteId]?.value(map),
      createdAt: fields[OrganizationField.createdAt]?.value(map),
      updatedAt: fields[OrganizationField.updatedAt]?.value(map),
      name: fields[OrganizationField.name]?.value(map),
      activePlan: fields[OrganizationField.activePlan]?.value(map),
      color: fields[OrganizationField.color]?.value(map),
      userHasSeen: fields[OrganizationField.userHasSeen]?.value(map),
      owner: await fields[OrganizationField.owner]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, Organization model) async {
    final map = <String, dynamic>{};
    const contract = OrganizationDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.createdAt, model.createdAt);
    addField(map, contract.updatedAt, model.updatedAt);
    addField(map, contract.name, model.name);
    addField(map, contract.activePlan, model.activePlan);
    addField(map, contract.color, model.color);
    addField(map, contract.userHasSeen, model.userHasSeen);

    await saveNestedModel<User>(
        context, map, contract.ownerId, context.db.user, model.owner);
    return map;
  }
}
