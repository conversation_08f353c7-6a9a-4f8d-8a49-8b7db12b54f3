import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/person/person.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/person/person_db_contract.dart';

class PersonDbTranslator implements DbTranslator<Person> {
  const PersonDbTranslator();

  @override
  Set<Field> get nestedModelFields => personNestedModelFields;

  @override
  Future<Person> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Person(
      id: fields[PersonField.id]?.value(map),
      remoteId: fields[PersonField.remoteId]?.value(map),
      firstName: fields[PersonField.firstName]?.value(map),
      lastName: fields[PersonField.lastName]?.value(map),
      birthDate: fields[PersonField.birthDate]?.value(map),
      createdAt: fields[PersonField.createdAt]?.value(map),
      updatedAt: fields[PersonField.updatedAt]?.value(map),
      user: await fields[PersonField.user]?.nested(context, map),
      detail: await fields[PersonField.detail]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Person model) async {
    final map = <String, dynamic>{};
    const contract = PersonDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.firstName, model.firstName);
    addField(map, contract.lastName, model.lastName);
    addField(map, contract.birthDate, model.birthDate);
    addField(map, contract.createdAt, model.createdAt);
    addField(map, contract.updatedAt, model.updatedAt);

    await saveNestedModel<User>(
        context, map, contract.userId, context.db.user, model.user);
    return map;
  }
}
