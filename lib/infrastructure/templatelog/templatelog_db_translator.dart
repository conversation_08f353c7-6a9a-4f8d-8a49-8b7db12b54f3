import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_db_contract.dart';

class TemplatelogDbTranslator implements DbTranslator<Templatelog> {
  const TemplatelogDbTranslator();

  @override
  Set<TemplatelogField> get nestedModelFields => templatelogNestedModelFields;

  @override
  Future<Templatelog> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Templatelog(
      id: fields[TemplatelogField.id]?.value(map),
      remoteId: fields[TemplatelogField.remoteId]?.value(map),
      templateName: fields[TemplatelogField.templateName]?.value(map),
      fieldsMetadata:
          await fields[TemplatelogField.fieldsMetadata]?.nested(context, map),
      template: await fields[TemplatelogField.template]?.nested(context, map),
      defaultProject:
          await fields[TemplatelogField.defaultProject]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, Templatelog model) async {
    final map = <String, dynamic>{};
    const contract = TemplatelogDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.templateName, model.templateName);

    await saveNestedModel<Template>(
        context, map, contract.templateId, context.db.template, model.template);
    await saveNestedModel<Project>(context, map, contract.defaultProjectId,
        context.db.project, model.defaultProject);

    return map;
  }
}
