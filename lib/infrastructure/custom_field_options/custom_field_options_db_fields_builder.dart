import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/custom_field_options/custom_field_options.dart';
import 'package:bitacora/domain/custom_field_options/custom_field_options_fields_builder.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_db_contract.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';

class CustomFieldOptionsDbFieldsBuilder extends DbFieldsBuilder
    implements CustomFieldOptionsFieldsBuilder {
  CustomFieldOptionsDbFieldsBuilder() {
    _id();
  }

  CustomFieldOptionsDbContract get contract =>
      const CustomFieldOptionsDbContract();

  CustomFieldOptionsDbFieldsBuilder _id() {
    addField(
      CustomFieldOptionsField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldOptionsDbFieldsBuilder remoteId() {
    addField(
      CustomFieldOptionsField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldOptionsFieldsBuilder isRequired() {
    addField(
      CustomFieldOptionsField.isRequired,
      DbField(
        column: contract.isRequired,
        valueBuilder: (v) => CustomFieldOptionsIsRequired(v == 1),
      ),
    );
    return this;
  }

  @override
  CustomFieldOptionsFieldsBuilder placeholder() {
    addField(
      CustomFieldOptionsField.placeholder,
      DbField(
        column: contract.placeholder,
        valueBuilder: (v) => CustomFieldOptionsPlaceholder(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldOptionsFieldsBuilder customField(Fields fields) {
    addField(
      CustomFieldOptionsField.customField,
      DbField(
          key: CustomFieldOptionsField.customField,
          column: contract.customFieldId,
          nestedFields: fields as DbFields,
          nestedBuilder: (nestedContext, value) =>
              nestedContext.db.customField.find(nestedContext, LocalId(value))),
    );
    return this;
  }
}
