import 'package:bitacora/domain/common/value_object/value_object.dart';

enum SyncMetadataCollectionTypeValue {
  resource('resources'),
  resourceCategory('resource_categories'),
  resourceAggregation('resource_aggregations'),
  qrCode('qr_codes'),
  feedPost('posts'),
  user('users'),
  personDetail('person_details'),
  customField('custom_fields'),
  template('templates'),
  signature('signatures'),
  entryGroup('entry_groups'),
  templateCondition('template_conditions');

  final String apiKey;

  const SyncMetadataCollectionTypeValue(this.apiKey);
}

class SyncMetadataCollectionType
    extends ValueObject<SyncMetadataCollectionTypeValue> {
  SyncMetadataCollectionType(super.value);

  static SyncMetadataCollectionType resource =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.resource);
  static SyncMetadataCollectionType resourceCategory =
      SyncMetadataCollectionType(
          SyncMetadataCollectionTypeValue.resourceCategory);
  static SyncMetadataCollectionType resourceAggregation =
      SyncMetadataCollectionType(
          SyncMetadataCollectionTypeValue.resourceAggregation);
  static SyncMetadataCollectionType qrCode =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.qrCode);
  static SyncMetadataCollectionType feedPost =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.feedPost);
  static SyncMetadataCollectionType user =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.user);
  static SyncMetadataCollectionType personDetail =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.personDetail);
  static SyncMetadataCollectionType customField =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.customField);
  static SyncMetadataCollectionType template =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.template);
  static SyncMetadataCollectionType signature =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.signature);
  static SyncMetadataCollectionType entryGroup =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.entryGroup);
  static SyncMetadataCollectionType templateCondition =
      SyncMetadataCollectionType(SyncMetadataCollectionTypeValue.templateCondition);

  factory SyncMetadataCollectionType.fromDbValue(int value) =>
      SyncMetadataCollectionType(
          SyncMetadataCollectionTypeValue.values[value - 1]);

  @override
  bool isSameType(Object other) {
    return other is SyncMetadataCollectionType;
  }

  @override
  get dbValue => value.index + 1;

  @override
  get displayValue => value.apiKey;

  @override
  get apiValue => value.apiKey;
}
