import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/value/entry_source_transcription.dart';
import 'package:bitacora/domain/entry_source/value/entry_source_metadata.dart';
import 'package:bitacora/domain/entry_source/value/entry_source_type.dart';

export 'package:bitacora/domain/entry/value/entry_source_transcription.dart';
export 'package:bitacora/domain/entry_source/value/entry_source_type.dart';
export 'package:bitacora/domain/entry_source/value/entry_source_metadata.dart';

class EntrySource extends Model {
  final EntrySourceType? type;
  final EntrySourceMetadata? metadata;
  final EntrySourceTranscription? transcription;
  final Entry? entry;
  final Attachment? attachment;

  EntrySource({
    super.id,
    this.type,
    this.metadata,
    this.entry,
    this.attachment,
    this.transcription,
  }) : super(remoteId: null);

  @override
  Map<Field, dynamic> get fields => {
        EntrySourceField.type: type,
        EntrySourceField.metadata: metadata,
        EntrySourceField.entry: entry,
        EntrySourceField.attachment: attachment
      };

  EntrySource copyWith({
    LocalId? id,
    EntrySourceType? type,
    EntrySourceMetadata? metadata,
    Entry? entry,
  }) {
    return EntrySource(
      id: id ?? this.id,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
      entry: entry ?? this.entry,
      attachment: attachment,
    );
  }
}

enum EntrySourceField with Field {
  id,
  remoteId,
  type,
  metadata,
  entry,
  attachment,
}

const entrySourceNestedModelFields = {EntrySourceField.entry};
