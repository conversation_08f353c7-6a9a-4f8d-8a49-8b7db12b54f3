import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata_fields_builder.dart';
import 'package:bitacora/infrastructure/db_context.dart';

abstract class EntryMetadataRepository<C extends RepositoryQueryContext,
        F extends EntryMetadataFieldsBuilder>
    extends RepositoryTable<EntryMetadata, C, F> {
  Future<List<EntryMetadata>> findAll(C context, LocalId entryId);

  Future<void> saveAll(DbContext context, Mutation<Entry> entryMutation);

  Future<void> deleteAll(DbContext context, LocalId entryId);
}
