import 'package:bitacora/domain/common/value_object/value_object.dart';

enum UserInviteStatus {
  pending,
  failed,
}

extension IndexedUserInviteStatus on UserInviteStatus {
  int get dbValue {
    switch (this) {
      case UserInviteStatus.pending:
        return 0;
      case UserInviteStatus.failed:
        return 1;
      }
  }

  static UserInviteStatus fromDbValue(int value) {
    switch (value) {
      case 0:
        return UserInviteStatus.pending;
      case 1:
        return UserInviteStatus.failed;
      default:
        throw 'UserInvite status $value unrecognized';
    }
  }
}

class UserInviteStatusValueObject extends ValueObject<UserInviteStatus> {
  UserInviteStatusValueObject(super.value);

  factory UserInviteStatusValueObject.fromDbValue(int dbValue) {
    return UserInviteStatusValueObject(
        IndexedUserInviteStatus.fromDbValue(dbValue));
  }

  @override
  bool isSameType(Object other) {
    return other is UserInviteStatusValueObject;
  }

  @override
  int get dbValue => value.dbValue;
}
