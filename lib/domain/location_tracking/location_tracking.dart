import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/location_point/location_point.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_created_at.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_is_local.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_owner_type.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_status.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_uuid.dart';
import 'package:bitacora/domain/organization/organization.dart';

export 'package:bitacora/domain/common/value_object/common.dart';
export 'package:bitacora/domain/location_tracking/value/location_tracking_is_local.dart';
export 'package:bitacora/domain/location_tracking/value/location_tracking_status.dart';
export 'package:bitacora/domain/location_tracking/value/location_tracking_uuid.dart';

class LocationTracking extends Model {
  final LocationTrackingUuid? uuid;
  final LocationTrackingStatus? status;
  final List<LocationPoint>? points;
  final LocationTrackingIsLocal? isLocal;
  final LocationTrackingOwnerType? ownerType;
  final LocationTrackingCreatedAt? createdAt;

  final Entry? entry;
  final Organization? organization;

  const LocationTracking({
    super.id,
    super.remoteId,
    this.uuid,
    this.status,
    this.points,
    this.isLocal,
    this.ownerType,
    this.createdAt,
    this.entry,
    this.organization,
  });

  LocationTracking copyWith({
    LocalId? id,
    RemoteId? remoteId,
    LocationTrackingUuid? uuid,
    LocationTrackingStatus? status,
    List<LocationPoint>? points,
    LocationTrackingIsLocal? isLocal,
    LocationTrackingCreatedAt? createdAt,
  }) {
    return LocationTracking(
      id: id ?? this.id,
      remoteId: remoteId ?? this.remoteId,
      uuid: uuid ?? this.uuid,
      status: status ?? this.status,
      points: points ?? this.points,
      isLocal: isLocal ?? this.isLocal,
      createdAt: createdAt ?? this.createdAt,
      ownerType: ownerType,
      entry: entry,
      organization: organization,
    );
  }

  bool get isSynced => remoteId?.value != null;

  bool get isStarted => status == LocationTrackingStatus.started;

  bool get isFinished => status == LocationTrackingStatus.finished;

  @override
  Map<Field, dynamic> get fields => {
        LocationTrackingField.id: id,
        LocationTrackingField.remoteId: remoteId,
        LocationTrackingField.uuid: uuid,
        LocationTrackingField.status: status,
        LocationTrackingField.points: points,
        LocationTrackingField.isLocal: isLocal,
        LocationTrackingField.ownerType: ownerType,
        LocationTrackingField.createdAt: createdAt,
        LocationTrackingField.entry: entry,
        LocationTrackingField.organization: organization,
      };
}

enum LocationTrackingField with Field {
  id,
  remoteId,
  uuid,
  status,
  points,
  isLocal,
  ownerType,
  createdAt,
  entry,
  organization,
}

const locationTrackingNestedModelFields = {
  LocationTrackingField.points,
  LocationTrackingField.organization,
};
