import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/resource_category/value/resource_category_name.dart';
import 'package:bitacora/domain/resource_category/value/resource_category_order.dart';

class ResourceCategory extends Model {
  final ResourceCategoryName? name;
  final ResourceCategoryOrder? order;

  final ResourceCategory? parent;
  final Organization? organization;

  ResourceCategory({
    super.id,
    super.remoteId,
    this.name,
    this.order,
    this.parent,
    this.organization,
  });

  ResourceCategory copyWith({
    Organization? organization,
  }) {
    return ResourceCategory(
      id: id,
      remoteId: remoteId,
      name: name,
      order: order,
      parent: parent,
      organization: organization ?? this.organization,
    );
  }

  @override
  Map<Field, dynamic> get fields => {
        ResourceCategoryField.id: id,
        ResourceCategoryField.remoteId: remoteId,
        ResourceCategoryField.name: name,
        ResourceCategoryField.order: order,
        ResourceCategoryField.parent: parent,
        ResourceCategoryField.organization: organization,
      };
}

enum ResourceCategoryField with Field {
  id,
  remoteId,
  name,
  order,
  parent,
  organization,
}

const resourceCategoryNestedModelFields = {
  ResourceCategoryField.parent,
  ResourceCategoryField.organization,
};
