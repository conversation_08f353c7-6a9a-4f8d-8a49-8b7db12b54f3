import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/repository_cache.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/foundation.dart';

// FIXME: dbTable.find -> read from cache if queried fields are in cached model

abstract class RepositoryTableCache<T extends Model> {
  final List<CacheIdResolver<T>> _idResolvers = <CacheIdResolver<T>>[
    _IdResolver(),
    _RemoteIdResolver(),
  ];

  final Map<LocalId, T> _map = <LocalId, T>{};

  RepositoryTableCache();

  LocalId? resolveId(Model? model) {
    if (model == null) {
      return null;
    }

    for (final idResolver in _idResolvers) {
      final id = idResolver.resolve(model);
      if (id != null) {
        return id;
      }
    }
    return null;
  }

  T? cachedModel(RepositoryCache cache, LocalId id) {
    final model = _map[id];
    if (model == null) {
      return null;
    }
    return completeCachedModel(cache, model);
  }

  @protected
  T? completeCachedModel(RepositoryCache cache, T model) => model;

  @protected
  bool isContainedIn(RepositoryCache cache, T a, T b);

  @protected
  T merge(T a, T b);

  @protected
  T prepareToCache(RepositoryCache cache, LocalId id, T model);

  /// Returns a non-null id if and only if the model is equivalent to what we
  /// have in cache (model === cache)
  LocalId? cachedIdWithFieldChecks(RepositoryCache cache, T model) {
    final id = resolveId(model);
    if (id == null) {
      return null;
    }

    final cached = cachedModel(cache, id);
    if (cached == null) {
      return null;
    }

    if (!isContainedIn(cache, model, cached)) {
      return null;
    }

    return id;
  }

  T? cachedModelWithFieldChecks(
      RepositoryCache cache, ValueObject id, RepositoryQueryContext context) {
    final emptyModel = EmptyModel(
      id: (id is LocalId) ? id : null,
      remoteId: (id is RemoteId) ? id : null,
    );

    final resolvedId = resolveId(emptyModel);
    if (resolvedId == null) {
      return null;
    }

    final cached = cachedModel(cache, resolvedId);
    if (cached == null) {
      return null;
    }

    if (!_doesModelContainQueryFields(cached, context)) {
      return null;
    }

    return cached;
  }

  bool _doesModelContainQueryFields(
      Model model, RepositoryQueryContext context) {
    final modelFields = model.fields;
    final queryFields = context.fields!.keys;

    for (final queryField in queryFields) {
      if (modelFields[queryField] == null) {
        return false;
      }
    }

    for (final modelField in modelFields.entries) {
      final nestedModel = modelField.value;
      if (nestedModel is Model &&
          queryFields.contains(modelField.key) &&
          !_doesModelContainQueryFields(
              nestedModel, context.nested(modelField.key)!)) {
        return false;
      }
    }
    return true;
  }

  void save(RepositoryCache cache, LocalId id, T model) {
    final cachedModel = _map[id];
    _saveToCache(
      prepareToCache(
        cache,
        id,
        cachedModel == null ? model : merge(cachedModel, model),
      ),
    );
  }

  void _saveToCache(T model) {
    _map[model.id!] = model;
    for (final idResolver in _idResolvers) {
      idResolver.onSaveToCache(model);
    }
  }

  void clear() {
    logger.i('db:cache:$T clearing map ${_map.length}');
    _map.clear();
    for (final idResolver in _idResolvers) {
      idResolver.clear();
    }
  }
}

bool isNullOrEqual(Object? a, Object? b) {
  return a == null ? true : a == b;
}

bool isNestedContainedIn<T extends Model>(
  RepositoryCache cache,
  T? a,
  T? b,
) {
  if (a == null) {
    return true;
  } else if (b == null) {
    return false;
  }
  return cache.table<T>()?.isContainedIn(cache, a, b) ?? false;
}

abstract class CacheIdResolver<T extends Model> {
  LocalId? resolve(Model model);

  void onSaveToCache(T model) {}

  void clear() {}
}

class _IdResolver<T extends Model> extends CacheIdResolver<T> {
  @override
  LocalId? resolve(Model model) {
    if (model.id != null) {
      return model.id;
    }
    return null;
  }
}

class _RemoteIdResolver<T extends Model> extends CacheIdResolver<T> {
  final Map<RemoteId, LocalId> _remoteToLocalIdsMap = <RemoteId, LocalId>{};

  @override
  LocalId? resolve(Model model) {
    if (model.remoteId == null) {
      return null;
    }
    return _remoteToLocalIdsMap[model.remoteId];
  }

  @override
  void onSaveToCache(T model) {
    if (model.remoteId?.value != null) {
      _remoteToLocalIdsMap[model.remoteId!] = model.id!;
    }
  }

  @override
  void clear() {
    _remoteToLocalIdsMap.clear();
  }
}
