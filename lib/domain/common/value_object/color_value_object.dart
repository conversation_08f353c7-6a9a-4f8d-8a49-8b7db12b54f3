import 'dart:ui';

import 'package:bitacora/domain/common/value_object/value_object.dart';

class ColorValueObject extends ValueObject<Color?> {
  const ColorValueObject(super.value);

  @override
  int? get dbValue => value?.toARGB32();

  @override
  int? get apiValue => dbValue;
}

class NonNullableColorValueObject extends ValueObject<Color> {
  const NonNullableColorValueObject(super.value);

  @override
  int get dbValue => value.toARGB32();

  @override
  int get apiValue => dbValue;
}
