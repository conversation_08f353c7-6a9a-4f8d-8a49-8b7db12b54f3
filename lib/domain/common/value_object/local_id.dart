import 'package:bitacora/domain/common/value_object/common.dart';

class LocalId extends NonNullableIntValueObject {
  const LocalId(super.value);

  @override
  bool isSameType(Object other) {
    return other is LocalId;
  }
}

class NullableLocalId extends LocalId {
  static const int nullValue = -1;

  final int? actualValue;

  const NullableLocalId(int? value)
      : actualValue = value,
        super(value ?? nullValue);

  @override
  bool isSameType(Object other) {
    return other is LocalId || other is NullableLocalId;
  }
}
