import 'package:bitacora/domain/access/access_repository.dart';
import 'package:bitacora/domain/address/address_repository.dart';
import 'package:bitacora/domain/attachment/attachment_repository.dart';
import 'package:bitacora/domain/avatar/avatar_repository.dart';
import 'package:bitacora/domain/common/repository_cache.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/custom_field/custom_field_repository.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value_repository.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata_repository.dart';
import 'package:bitacora/domain/custom_field_options/custom_field_options_repository.dart';
import 'package:bitacora/domain/email/email_repository.dart';
import 'package:bitacora/domain/entry/entry_repository.dart';
import 'package:bitacora/domain/entry_group/entry_group_repository.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry_repository.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata_repository.dart';
import 'package:bitacora/domain/entry_source/entry_source_repository.dart';
import 'package:bitacora/domain/feature_flag/feature_flag_repository.dart';
import 'package:bitacora/domain/feed_post/feed_post_repository.dart';
import 'package:bitacora/domain/inventorylog/inventorylog_repository.dart';
import 'package:bitacora/domain/location_point/location_point_repository.dart';
import 'package:bitacora/domain/location_tracking/location_tracking_repository.dart';
import 'package:bitacora/domain/open_state/open_state_repository.dart';
import 'package:bitacora/domain/organization/organization_repository.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation_repository.dart';
import 'package:bitacora/domain/person/person_repository.dart';
import 'package:bitacora/domain/person_detail/person_detail_repository.dart';
import 'package:bitacora/domain/personnellog/personnellog_repository.dart';
import 'package:bitacora/domain/phone/phone_repository.dart';
import 'package:bitacora/domain/progresslog/progresslog_repository.dart';
import 'package:bitacora/domain/project/project_repository.dart';
import 'package:bitacora/domain/qr_code/qr_code_repository.dart';
import 'package:bitacora/domain/report/report_repository.dart';
import 'package:bitacora/domain/report_template/report_template_repository.dart';
import 'package:bitacora/domain/resource/resource_repository.dart';
import 'package:bitacora/domain/resource_category/resource_category_repository.dart';
import 'package:bitacora/domain/signature/signature_repository.dart';
import 'package:bitacora/domain/sync_metadata/sync_metadata_repository.dart';
import 'package:bitacora/domain/tag/tag_repository.dart';
import 'package:bitacora/domain/template/template_repository.dart';
import 'package:bitacora/domain/template_block/template_block_repository.dart';
import 'package:bitacora/domain/template_condition/template_condition_repository.dart';
import 'package:bitacora/domain/template_group/template_group_repository.dart';
import 'package:bitacora/domain/templatelog/templatelog_repository.dart';
import 'package:bitacora/domain/user/user_repository.dart';
import 'package:bitacora/domain/user_invite/user_invite_repository.dart';
import 'package:bitacora/domain/worklog/worklog_repository.dart';

abstract class Repository<T extends RepositoryQueryContext> {
  Future<R> transaction<R>(Future<R> Function(T context) action);

  Future<void> close();

  T context({
    Fields? fields,
    Cursor? cursor,
    QueryScope? queryScope,
    Map<String, dynamic>? props,
  });

  QueryScope queryScope({
    LocalId? userId,
    LocalId? orgId,
    LocalId? projectId,
    CustomFieldMetadata? metadataFilter,
  }) {
    return QueryScope(
      userId: userId,
      orgId: orgId,
      projectId: projectId,
      metadataFilter: metadataFilter,
    );
  }

  Future<U> query<U>(
    RepositoryQuery<U> repositoryQuery, {
    T? context,
  });

  Future<void> nuke();

  void markDirty();

  RepositoryCache get cache;

  Future<String> get path;

  AccessRepository get access;

  AddressRepository get address;

  AttachmentRepository get attachment;

  AvatarRepository get avatar;

  CustomFieldRepository get customField;

  CustomFieldMetadataRepository get customFieldMetadata;

  CustomFieldOptionsRepository get customFieldOptions;

  CustomFieldAllowedValueRepository get customFieldAllowedValue;

  TemplateBlockRepository get templateBlock;

  TemplateConditionRepository get templateCondition;

  TemplateGroupRepository get templateGroup;

  EntryRepository get entry;

  EntryGroupRepository get entryGroup;

  EntryGroupEntryRepository get entryGroupEntry;

  EntrySourceRepository get entrySource;

  EntryMetadataRepository get entryMetadata;

  EmailRepository get email;

  FeedPostRepository get feedPost;

  InventorylogRepository get inventorylog;

  UserInviteRepository get userInvite;

  OpenStateRepository get openState;

  OrganizationRepository get organization;

  PersonRepository get person;

  PersonDetailRepository get personDetail;

  OutgoingMutationRepository get outgoingMutation;

  PersonnellogRepository get personnellog;

  PhoneRepository get phone;

  ProgresslogRepository get progresslog;

  ProjectRepository get project;

  QrCodeRepository get qrCode;

  ReportRepository get report;

  ReportTemplateRepository get reportTemplate;

  ResourceRepository get resource;

  ResourceCategoryRepository get resourceCategory;

  SignatureRepository get signature;

  SyncMetadataRepository get syncMetadata;

  TagRepository get tag;

  TemplateRepository get template;

  TemplatelogRepository get templatelog;

  UserRepository get user;

  WorklogRepository get worklog;

  LocationPointRepository get locationPoint;

  LocationTrackingRepository get locationTracking;

  FeatureFlagRepository get featureFlag;
}
