import 'dart:async';

import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:flutter/material.dart';

typedef LiveModelLoader<T> = Future<T?> Function();

class LiveModel<T extends Model> extends ValueNotifier<T?> {
  bool _mounted = true;
  final LiveModelLoader<T> loader;
  late final StreamSubscription<Mutation<T>>? listener;

  LiveModel(
    T? value,
    Stream<Mutation<T>> mutations,
    this.loader,
  ) : super(value) {
    listener = value?.id == null
        ? null
        : mutations.listen((event) async {
            if ((event.id != null && event.id == value?.id) ||
                event.type == MutationType.unknown) {
              final newValue = await loader();
              if (_mounted) {
                this.value = newValue;
              }
            }
          });
  }

  @override
  void dispose() {
    _mounted = false;
    listener?.cancel();
    super.dispose();
  }
}
