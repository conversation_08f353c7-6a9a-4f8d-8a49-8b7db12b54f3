import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/personnellog/value/personnellog_entrance.dart';
import 'package:bitacora/domain/personnellog/value/personnellog_exit.dart';
import 'package:bitacora/domain/personnellog/value/personnellog_minutes.dart';
import 'package:bitacora/domain/personnellog/value/personnellog_name.dart';
import 'package:bitacora/domain/personnellog/value/personnellog_sublocation.dart';
import 'package:bitacora/domain/project/project.dart';

export 'package:bitacora/domain/personnellog/value/personnellog_entrance.dart';
export 'package:bitacora/domain/personnellog/value/personnellog_exit.dart';
export 'package:bitacora/domain/personnellog/value/personnellog_minutes.dart';
export 'package:bitacora/domain/personnellog/value/personnellog_name.dart';
export 'package:bitacora/domain/personnellog/value/personnellog_sublocation.dart';

class Personnellog extends Extension {
  final PersonnellogName? name;
  final PersonnellogSublocation? sublocation;
  final PersonnellogEntrance? entrance;
  final PersonnellogExit? exit;
  final PersonnellogMinutes? minutes;

  final Project? project;

  const Personnellog({
    super.id,
    super.remoteId,
    this.name,
    this.sublocation,
    this.entrance,
    this.exit,
    this.minutes,
    this.project,
  });

  Personnellog copyWith({
    PersonnellogName? name,
    PersonnellogEntrance? entrance,
    PersonnellogExit? exit,
  }) {
    return Personnellog(
      id: id,
      remoteId: remoteId,
      name: name ?? this.name,
      sublocation: sublocation,
      entrance: entrance ?? this.entrance,
      exit: exit ?? this.exit,
      minutes: minutes,
      project: project,
    );
  }

  @override
  ExtensionType get extensionType => ExtensionType.personnellog;

  @override
  Map<Field, dynamic> get fields => {
        PersonnellogField.id: id,
        PersonnellogField.remoteId: remoteId,
        PersonnellogField.name: name,
        PersonnellogField.sublocation: sublocation,
        PersonnellogField.entrance: entrance,
        PersonnellogField.exit: exit,
        PersonnellogField.minutes: minutes,
        PersonnellogField.project: project,
      };
}

enum PersonnellogField with Field {
  id,
  remoteId,
  name,
  sublocation,
  entrance,
  exit,
  minutes,
  project,
}

const personnellogNestedModelFields = {
  PersonnellogField.project,
};
