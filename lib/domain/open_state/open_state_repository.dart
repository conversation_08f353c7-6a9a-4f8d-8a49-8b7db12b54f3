import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/domain/open_state/open_state_fields_builder.dart';

abstract class OpenStateRepository<C extends RepositoryQueryContext,
    F extends OpenStateFieldsBuilder> extends RepositoryTable<OpenState, C, F> {
  Future<Entry?> entry(C context, LocalId openStateId);

  Future<List<Entry>> progresslogs(C context, LocalId parentEntryId);
}
