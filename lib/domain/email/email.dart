import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/person_detail_contact_type.dart';
import 'package:bitacora/domain/email/value/email_created_at.dart';
import 'package:bitacora/domain/email/value/email_updated_at.dart';
import 'package:bitacora/domain/email/value/email_value.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';

export 'package:bitacora/domain/common/person_detail_contact_type.dart';
export 'package:bitacora/domain/email/value/email_created_at.dart';
export 'package:bitacora/domain/email/value/email_updated_at.dart';
export 'package:bitacora/domain/email/value/email_value.dart';

class Email extends Model {
  final EmailValue? value;
  final PersonDetailContactType? type;
  final EmailUpdatedAt? updatedAt;
  final EmailCreatedAt? createdAt;

  final PersonDetail? personDetail;

  Email({
    super.id,
    super.remoteId,
    this.value,
    this.type,
    this.updatedAt,
    this.createdAt,
    this.personDetail,
  });

  @override
  Map<Field, dynamic> get fields => {
        EmailField.id: id,
        EmailField.remoteId: remoteId,
        EmailField.value: value,
        EmailField.type: type,
        EmailField.updatedAt: updatedAt,
        EmailField.createdAt: createdAt,
        EmailField.personDetail: personDetail,
      };
}

enum EmailField with Field {
  id,
  remoteId,
  value,
  type,
  updatedAt,
  createdAt,
  personDetail
}

const emailNestedModelFields = {EmailField.personDetail};
