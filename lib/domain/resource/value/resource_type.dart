import 'package:bitacora/domain/common/value_object/value_object.dart';

enum ResourceTypeValue {
  s3Object,
  url;

  const ResourceTypeValue();

  static ResourceTypeValue fromApiValue(String apiValue) {
    switch (apiValue) {
      case 'S3_OBJECT':
        return ResourceTypeValue.s3Object;
      case 'URL':
        return ResourceTypeValue.url;
      default:
        throw 'ResourceTypeValue:fromApiValue [$apiValue] not supported';
    }
  }
}

class ResourceType extends ValueObject<ResourceTypeValue> {
  ResourceType(super.value);

  static ResourceType get s3Object => ResourceType(ResourceTypeValue.s3Object);

  static ResourceType get url => ResourceType(ResourceTypeValue.url);

  factory ResourceType.fromApiValue(String apiValue) =>
      ResourceType(ResourceTypeValue.fromApiValue(apiValue));

  factory ResourceType.fromDbValue(int value) =>
      ResourceType(ResourceTypeValue.values[value - 1]);

  @override
  bool isSameType(Object other) {
    return other is ResourceType;
  }

  @override
  int get dbValue => value.index + 1;
}
