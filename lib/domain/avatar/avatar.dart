import 'package:bitacora/domain/avatar/value/avatar_is_downloaded.dart';
import 'package:bitacora/domain/avatar/value/avatar_path.dart';
import 'package:bitacora/domain/avatar/value/avatar_person_detail_id.dart';
import 'package:bitacora/domain/avatar/value/avatar_s3_key.dart';
import 'package:bitacora/domain/avatar/value/avatar_transfer_state.dart';
import 'package:bitacora/domain/common/model.dart';

class Avatar extends Model {
  final AvatarPath? path;
  final AvatarS3Key? s3Key;
  final AvatarIsDownloaded? isDownloaded;
  final AvatarTransferState? transferState;
  final AvatarPersonDetailId? personDetailId;

  Avatar({
    super.id,
    this.path,
    this.s3Key,
    this.isDownloaded,
    this.transferState,
    this.personDetailId,
  });

  @override
  Map<Field, dynamic> get fields => {
        AvatarField.id: id,
        AvatarField.remoteId: remoteId,
        AvatarField.path: path,
        AvatarField.s3Key: s3Key,
        AvatarField.isDownloaded: isDownloaded,
        AvatarField.transferState: transferState,
        AvatarField.personDetailId: personDetailId,
      };
}

enum AvatarField with Field {
  id,
  remoteId,
  path,
  s3Key,
  isDownloaded,
  transferState,
  personDetailId,
}

const avatarNestedModelFields = <Field>{};
