import 'package:bitacora/infrastructure/projectentriesproject/project_entries_project_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$ProjectEntriesProjectDbContract tests', () {
    test('Create $ProjectEntriesProjectDbContract', () {
      expectRemovingSpaces(
        const ProjectEntriesProjectDbContract().create,
        '''
        CREATE TABLE projectEntriesProject (
          pep_projectId INTEGER NOT NULL,
          pep_entryId INTEGER NOT NULL,
          PRIMARY KEY (pep_projectId, pep_entryId)
        )
        ''',
      );
    });
  });
}
