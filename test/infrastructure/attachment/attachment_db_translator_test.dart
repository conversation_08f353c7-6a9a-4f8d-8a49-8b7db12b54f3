import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/attachment/attachment_db_translator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../db_fields_test_util.dart';
import '../entry/mocks.dart';
import '../mocks.dart';

void main() {
  group('$AttachmentDbTranslator tests', () {
    test('id', () {
      const value = LocalId(12);
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.id,
        value,
        (attachment) => attachment.id == value,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        const Attachment(id: value),
        {'a_id': value.dbValue},
        prepareDb: _prepareDbForSave,
      );
    });

    test('s3key', () {
      final value = AttachmentS3Key('s3-key');
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.s3Key,
        value,
        (attachment) => attachment.s3Key == value,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        Attachment(s3Key: value),
        {'a_s3Key': value.dbValue},
        prepareDb: _prepareDbForSave,
      );
    });

    test('name', () {
      final value = AttachmentName('file.zip');
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.name,
        value,
        (attachment) => attachment.name == value,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        Attachment(name: value),
        {'a_name': value.dbValue},
        prepareDb: _prepareDbForSave,
      );
    });

    test('isUploaded', () {
      final value = AttachmentIsUploaded(true);
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.isUploaded,
        value,
        (attachment) => attachment.isUploaded == value,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        Attachment(isUploaded: value),
        {'a_isUploaded': value.dbValue},
        prepareDb: _prepareDbForSave,
      );
    });

    test('isDownloaded', () {
      final value = AttachmentIsDownloaded(true);
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.isDownloaded,
        value,
        (attachment) => attachment.isDownloaded == value,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        Attachment(isDownloaded: value),
        {'a_isDownloaded': value.dbValue},
        prepareDb: _prepareDbForSave,
      );
    });

    test('transferState', () {
      final value =
          AttachmentTransferStateValueObject(AttachmentTransferState.failed);
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.transferState,
        value,
        (attachment) => attachment.transferState == value,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        Attachment(transferState: value),
        {'a_transferState': value.dbValue},
        prepareDb: _prepareDbForSave,
      );
    });

    test('transferAttempts', () {
      const value = AttachmentTransferAttempts(3);
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.transferAttempts,
        value,
        (attachment) => attachment.transferAttempts == value,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        const Attachment(transferAttempts: value),
        {'a_transferAttempts': value.dbValue},
        prepareDb: _prepareDbForSave,
      );
    });

    test('path', () {
      const value = AttachmentPath('/path/to/file/');
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.path,
        value,
        (attachment) => attachment.path == value,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        const Attachment(path: value),
        {'a_path': value.dbValue},
        prepareDb: _prepareDbForSave,
      );
    });

    test('comments', () {
      const value = AttachmentComments('comments...');
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.comments,
        value,
        (attachment) => attachment.comments == value,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        const Attachment(comments: value),
        {'a_comments': value.dbValue},
        prepareDb: _prepareDbForSave,
      );
    });

    test('entry with id', () {
      const value = Entry(id: LocalId(1234));
      translatorTestFromDb<Attachment>(
        const AttachmentDbTranslator(),
        AttachmentField.entry,
        value,
        (attachment) => attachment.entry == value,
        isNested: true,
      );
      translatorTestToDb<Attachment>(
        const AttachmentDbTranslator(),
        const Attachment(entry: value),
        {'a_entryId': value.id!.dbValue},
        prepareDb: (context, db) => _prepareDbForSave(context, db, value),
        verifyDb: (context, db) {
          final entryRepository = db.entry;
          verifyNever(() => entryRepository.save(context, value));
        },
      );
    });
  });
}

void _prepareDbForSave(
  MockDbContext context,
  MockDbRepository db, [
  Entry? entry,
]) {
  final entryDbTable = MockEntryDbTable();
  when(() => db.entry).thenReturn(entryDbTable);
  if (entry != null) {
    when(() => entryDbTable.save(context, entry))
        .thenAnswer((_) => Future.value(entry.id!));
  }
}
