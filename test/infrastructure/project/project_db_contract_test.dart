import 'package:bitacora/infrastructure/project/project_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$ProjectDbContract tests', () {
    test('Create $ProjectDbContract', () {
      expectRemovingSpaces(
        const ProjectDbContract().create,
        '''
        CREATE TABLE project (
          p_id INTEGER PRIMARY KEY AUTOINCREMENT,
          p_remoteId INTEGER UNIQUE,
          p_organizationId INTEGER NOT NULL,
          p_name TEXT NOT NULL,
          p_description TEXT,
          p_addressTEXT,
          p_loc_longitudeREAL,
          p_loc_latitudeREAL,
          p_typeTEXT,
          p_isSyncable INTEGER,
          p_syncLastEntryUpdatedAt INTEGER,
          p_syncLastSyncTime INTEGER,
          p_syncNextPageToken TEXT
        )
        ''',
      );
    });
  });
}
