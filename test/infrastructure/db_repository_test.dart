import 'package:bitacora/infrastructure/access/access_db_contract.dart';
import 'package:bitacora/infrastructure/access_entry/access_entry_db_contract.dart';
import 'package:bitacora/infrastructure/address/address_db_contract.dart';
import 'package:bitacora/infrastructure/attachment/attachment_db_contract.dart';
import 'package:bitacora/infrastructure/avatar/avatar_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field/custom_field_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_allowed_value/custom_field_allowed_value_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_db_contract.dart';
import 'package:bitacora/infrastructure/db_lock.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/infrastructure/email/email_db_contract.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_db_contract.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/entry_source/entry_source_db_contract.dart';
import 'package:bitacora/infrastructure/feature_flag/feature_flag_db_contract.dart';
import 'package:bitacora/infrastructure/feed_post/feed_post_db_contract.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_contract.dart';
import 'package:bitacora/infrastructure/location_point/location_point_db_contract.dart';
import 'package:bitacora/infrastructure/location_tracking/location_tracking_db_contract.dart';
import 'package:bitacora/infrastructure/logging_executor.dart';
import 'package:bitacora/infrastructure/open_state/open_state_db_contract.dart';
import 'package:bitacora/infrastructure/organization/organization_db_contract.dart';
import 'package:bitacora/infrastructure/outgoing_mutation/outgoing_mutation_db_contract.dart';
import 'package:bitacora/infrastructure/person/person_db_contract.dart';
import 'package:bitacora/infrastructure/person_detail/person_detail_db_contract.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_db_contract.dart';
import 'package:bitacora/infrastructure/phone/phone_db_contract.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_contract.dart';
import 'package:bitacora/infrastructure/project/project_db_contract.dart';
import 'package:bitacora/infrastructure/projectentriesproject/project_entries_project_db_contract.dart';
import 'package:bitacora/infrastructure/qr_code/qr_code_db_contract.dart';
import 'package:bitacora/infrastructure/report/report_db_contract.dart';
import 'package:bitacora/infrastructure/report_template/report_template_db_contract.dart';
import 'package:bitacora/infrastructure/resource/resource_aggregation_db_contract.dart';
import 'package:bitacora/infrastructure/resource/resource_db_contract.dart';
import 'package:bitacora/infrastructure/resource_category/resource_category_db_contract.dart';
import 'package:bitacora/infrastructure/resource_category/resource_catergory_resource_db_contract.dart';
import 'package:bitacora/infrastructure/signature/signature_db_contract.dart';
import 'package:bitacora/infrastructure/sqflite_utils.dart';
import 'package:bitacora/infrastructure/sync_metadata/sync_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/tag/tag_db_contract.dart';
import 'package:bitacora/infrastructure/tag/tag_entry_db_contract.dart';
import 'package:bitacora/infrastructure/template/template_db_contract.dart';
import 'package:bitacora/infrastructure/template_block/template_block_db_contract.dart';
import 'package:bitacora/infrastructure/template_condition/template_condition_db_contract.dart';
import 'package:bitacora/infrastructure/template_group/template_group_db_contract.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_db_contract.dart';
import 'package:bitacora/infrastructure/user/user_db_contract.dart';
import 'package:bitacora/infrastructure/user_invite/user_invite_db_contract.dart';
import 'package:bitacora/infrastructure/user_location_tracking/user_location_tracking_db_contract.dart';
import 'package:bitacora/infrastructure/user_organization/user_organization_db_contract.dart';
import 'package:bitacora/infrastructure/worklog/worklog_db_contract.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

import '../mocks.dart';
import '../test_util.dart';
import '../util/file_system/mocks.dart';
import 'mocks.dart';

const String _kDbFileName = 'bitacora.db';

void main() {
  setUpAll(() {
    PathProviderPlatform.instance = MockPathProvider();
    SharedPreferences.setMockInitialValues({});
    PackageInfo.setMockInitialValues(
      appName: '',
      packageName: '',
      version: '0.0.1',
      buildNumber: '',
      buildSignature: '',
    );
  });

  group('$DbRepository tests', () {
    test('Injects new', () {
      expect(DbRepository() != DbRepository(), true);
    });

    test('Has default db file', () {
      final db = DbRepository();

      expect(db.file, _kDbFileName);
    });

    testWidgets('iOS has correct path', (tester) async {
      await withIOS(() async {
        final db = DbRepository();
        const pathDirectory = MockPathProvider.kLibraryPath;

        final dbPath = await db.path;

        expect(dbPath, path.join(pathDirectory, _kDbFileName));
      });
    });

    test('Android has correct path', () async {
      const pathDirectory = MockPathProvider.kStoragePath;
      final sqfliteUtils = MockSqfliteUtils();
      when(() => sqfliteUtils.databasesPath)
          .thenAnswer((_) => Future.value(pathDirectory));

      await withAndroid(() async {
        await withInjected<SqfliteUtils>(sqfliteUtils, () async {
          final db = DbRepository();

          final dbPath = await db.path;

          expect(dbPath, path.join(pathDirectory, _kDbFileName));
        });
      });
    });

    test('Opens database', () async {
      final database = MockDatabase();

      await withInjected3<SqfliteUtils, FileSystem, DbLock>(
        _mockSqfliteUtils(database),
        await mockFileSystem(),
        mockDbLock(),
        () async {
          final db = DbRepository();

          final opened = await db.executor(db.context());

          expect((opened as LoggingExecutor).executor, database);
        },
      );
    });

    test('Verify contract components.', () {
      final db = DbRepository();

      final contracts = db.contracts;

      final numTables = contracts.length;
      expect(
        contracts,
        const [
          AccessDbContract(),
          AccessEntryDbContract(),
          AddressDbContract(),
          AvatarDbContract(),
          AttachmentDbContract(),
          CustomFieldDbContract(),
          CustomFieldMetadataDbContract(),
          CustomFieldAllowedValueDbContract(),
          CustomFieldOptionsDbContract(),
          EntryDbContract(),
          EntryGroupDbContract(),
          EntryGroupEntryDbContract(),
          EntrySourceDbContract(),
          EntryMetadataDbContract(),
          EmailDbContract(),
          FeatureFlagDbContract(),
          FeedPostDbContract(),
          InventorylogDbContract(),
          LocationPointDbContract(),
          LocationTrackingDbContract(),
          OpenStateDbContract(),
          OrganizationDbContract(),
          OutgoingMutationDbContract(),
          PersonDbContract(),
          PersonDetailDbContract(),
          PersonnellogDbContract(),
          PhoneDbContract(),
          ProgresslogDbContract(),
          ProjectDbContract(),
          ProjectEntriesProjectDbContract(),
          QrCodeDbContract(),
          ReportDbContract(),
          ReportTemplateDbContract(),
          ResourceDbContract(),
          ResourceAggregationDbContract(),
          ResourceCategoryDbContract(),
          ResourceCategoryResourceDbContract(),
          SignatureDbContract(),
          SyncMetadataDbContract(),
          TagDbContract(),
          TagEntryDbContract(),
          TemplateDbContract(),
          TemplateBlockDbContract(),
          TemplateConditionDbContract(),
          TemplateGroupDbContract(),
          TemplatelogDbContract(),
          UserDbContract(),
          UserInviteDbContract(),
          UserLocationTrackingDbContract(),
          UserOrganizationDbContract(),
          WorklogDbContract(),
        ],
      );
      expect(contracts.map((e) => e.tableName).toSet().length, numTables);
      expect(contracts.map((e) => e.prefix).toSet().length, numTables);
    });

    test('Creates all contract tables', () async {
      final database = MockDatabase();
      when(() => database.execute(any())).thenAnswer((_) => Future.value());
      final sqfliteUtils = _mockSqfliteUtils(database);

      await withInjectedN(
        {
          SqfliteUtils: sqfliteUtils,
          DbLock: mockDbLock(),
        },
        () async {
          final db = DbRepository();

          await db.executor(db.context());

          final onCreateFn = verify(
            () => sqfliteUtils.openDatabase(
              _kDbFileName,
              version: any(named: 'version'),
              onCreate: captureAny(named: 'onCreate'),
              onUpgrade: any(named: 'onUpgrade'),
            ),
          ).captured[0] as OnDatabaseCreateFn;
          await onCreateFn(database, 1);
          for (final contract in db.contracts) {
            verify(() => database.execute(contract.create));
          }
        },
      );
    });

    test('Upgrades 1->2', () async {
      final database = MockDatabase();
      when(() => database.execute(any())).thenAnswer((_) => Future.value());
      final sqfliteUtils = _mockSqfliteUtils(database);

      final onVersionChangeFn = await withInjected2<SqfliteUtils, DbLock>(
        sqfliteUtils,
        mockDbLock(),
        () async {
          final db = DbRepository();

          await db.executor(db.context());

          return verify(
            () => sqfliteUtils.openDatabase(
              _kDbFileName,
              version: any(named: 'version'),
              onCreate: any(named: 'onCreate'),
              onUpgrade: captureAny(named: 'onUpgrade'),
            ),
          ).captured[0];
        },
      ) as OnDatabaseVersionChangeFn;
      await onVersionChangeFn(database, 1, 2);

      verify(() => database
          .execute('ALTER TABLE attachment ADD a_transferAttempts INTEGER'));
    });

    test('Closes database', () async {
      final database = MockDatabase();
      when(() => database.close()).thenAnswer((_) => Future.value());

      await withInjected2<SqfliteUtils, FileSystem>(
        _mockSqfliteUtils(database),
        await mockFileSystem(),
        () async {
          final db = DbRepository();
          await db.executor(db.context());

          await db.close();

          await awaitUntilStopsThrowing(() => verify(() => database.close()));
        },
      );
    });

    test('Nukes database', () async {
      final database = MockDatabase();
      final sqfliteUtils = _mockSqfliteUtils(database);
      when(() => sqfliteUtils.deleteDatabase(_kDbFileName))
          .thenAnswer((_) => Future.value());

      await withInjected<SqfliteUtils>(
        sqfliteUtils,
        () async {
          final db = DbRepository();
          await db.executor(db.context());

          await db.nuke();

          expect(
            () => db.executor(db.context()),
            throwsA('This db session ended.'),
          );

          verify(() => sqfliteUtils.deleteDatabase(_kDbFileName));
        },
      );
    });

    test('Mark as dirty', () async {
      final db = DbRepository();
      var dirtyTablesCounter = 0;
      onDirtyTable(_) {
        dirtyTablesCounter++;
      }

      db.access.getMutations().listen(onDirtyTable);
      db.entry.getMutations().listen(onDirtyTable);
      db.attachment.getMutations().listen(onDirtyTable);
      db.inventorylog.getMutations().listen(onDirtyTable);
      db.userInvite.getMutations().listen(onDirtyTable);
      db.openState.getMutations().listen(onDirtyTable);
      db.organization.getMutations().listen(onDirtyTable);
      db.personnellog.getMutations().listen(onDirtyTable);
      db.progresslog.getMutations().listen(onDirtyTable);
      db.project.getMutations().listen(onDirtyTable);
      db.tag.getMutations().listen(onDirtyTable);
      db.user.getMutations().listen(onDirtyTable);
      db.worklog.getMutations().listen(onDirtyTable);

      db.markDirty();

      await awaitUntil(() => dirtyTablesCounter == 13);
    });
  });
}

SqfliteUtils _mockSqfliteUtils(Database database) {
  final sqfliteUtils = MockSqfliteUtils();
  when(() => sqfliteUtils.databasesPath).thenAnswer((_) => Future.value(''));
  when(() => sqfliteUtils.openDatabase(
        _kDbFileName,
        version: any(named: 'version'),
        onCreate: any(named: 'onCreate'),
        onUpgrade: any(named: 'onUpgrade'),
      )).thenAnswer((_) => Future.value(database));
  return sqfliteUtils;
}
