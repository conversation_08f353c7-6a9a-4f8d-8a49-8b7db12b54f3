import 'dart:async';

import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/organization/organization_repository.dart';
import 'package:bitacora/util/fcm/fcm_organization_topic_repository_query.dart';
import 'package:bitacora/util/fcm/fcm_organization_topic_subscriptions_monitor.dart';
import 'package:bitacora/util/fcm/fcm_utils.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../domain/common/mocks.dart';
import '../../domain/organization/mocks.dart';
import 'mocks.dart';

void main() {
  group('FcmOrganizationTopicSubscriptionsMonitor tests', () {
    test('Syncs subscriptions', () async {
      final db = _mockRepository();
      final fcmUtils = MockFcmUtils();
      when(() => fcmUtils.syncSubscriptions(any()))
          .thenAnswer((_) => Future.value(null));

      await withInjected<FcmUtils>(
          fcmUtils, () => FcmOrganizationTopicSubscriptionsMonitor(db));

      verify(() => fcmUtils.syncSubscriptions(any()));
    });

    test('Syncs subscriptions on org mutation', () async {
      final streamController = StreamController<Mutation<Organization>>();
      final orgRepository =
          _mockOrgRepository(streamController: streamController);
      final db = _mockRepository(testOrgRepository: orgRepository);
      final fcmUtils = MockFcmUtils();
      when(() => fcmUtils.syncSubscriptions(any()))
          .thenAnswer((_) => Future.value(null));
      await withInjected<FcmUtils>(
          fcmUtils, () => FcmOrganizationTopicSubscriptionsMonitor(db));

      await withInjected<FcmUtils>(
          fcmUtils,
          () => streamController.sink
              .add(Mutation<Organization>(type: MutationType.insert)));

      await Future.microtask(
          () => verify(() => fcmUtils.syncSubscriptions(any())).called(2));
    });

    test('Sync subscribes to the same topics', () async {
      final streamController = StreamController<Mutation<Organization>>();
      final orgRepository =
          _mockOrgRepository(streamController: streamController);
      final db = _mockRepository(
        testOrgRepository: orgRepository,
        queryResult: List.generate(
            2, (index) => Organization(remoteId: RemoteId(index))),
      );
      final fcmUtils = MockFcmUtils();
      when(() => fcmUtils.syncSubscriptions(any()))
          .thenAnswer((_) => Future.value(null));
      await withInjected<FcmUtils>(
          fcmUtils, () => FcmOrganizationTopicSubscriptionsMonitor(db));

      await withInjected<FcmUtils>(
          fcmUtils,
          () => streamController
              .add(Mutation<Organization>(type: MutationType.insert)));

      await Future.microtask(
          () => verify(() => fcmUtils.syncSubscriptions(any())).called(1));
    });
  });
}

Repository _mockRepository({
  OrganizationRepository? testOrgRepository,
  List<Organization>? queryResult,
}) {
  final db = MockRepository();
  final orgRepository = testOrgRepository ?? _mockOrgRepository();
  when(() => db.organization).thenReturn(orgRepository);
  when(
    () => db.query(const FcmOrganizationTopicRepositoryQuery()),
  ).thenAnswer(
    (invocation) => Future.value(
      queryResult ??
          List.generate(2, (index) => mockOrganization(withRemoteId: true)),
    ),
  );
  return db;
}

OrganizationRepository _mockOrgRepository({
  StreamController<Mutation<Organization>>? streamController,
}) {
  final orgsRepository = MockOrganizationRepository();
  when(() => orgsRepository.getMutations()).thenAnswer(
    (_) {
      final controller = streamController ??
          StreamController<Mutation<Organization>>.broadcast();
      return controller.stream;
    },
  );
  return orgsRepository;
}
