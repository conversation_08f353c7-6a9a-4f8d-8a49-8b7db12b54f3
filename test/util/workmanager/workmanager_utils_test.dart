import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/sync/background/background_sync.dart';
import 'package:bitacora/util/background_work/background_work_utils.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/workmanager/cancelable_by_foreground.dart';
import 'package:bitacora/util/workmanager/workmanager_utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:workmanager/workmanager.dart';

import '../../application/mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../../test_util.dart';
import '../background_work/mocks.dart';
import '../mocks.dart';
import 'mocks.dart';

typedef BackgroundTaskHandler = Future<bool> Function(
    String taskName, Map<String, dynamic>? inputData);

void main() {
  group('$WorkmanagerUtils tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Injects same', () {
      expect(WorkmanagerUtils(), WorkmanagerUtils());
    });

    test('Injects same workmanager', () {
      expect(WorkmanagerInjector.get(), WorkmanagerInjector.get());
    });

    test('Initializes Workmanager', () {
      final workmanager = mockWorkmanager();
      final workmanagerUtils = WorkmanagerUtils();

      withAndroid(() => withInjected<Workmanager>(
            workmanager,
            () => workmanagerUtils.init(),
          ));

      verify(
        () => workmanager.initialize(
          workmanagerCallbackDispatcher,
          isInDebugMode: AppConfig().isDevToolsEnabled,
        ),
      );
    });

    test('registerSyncTask iOS', () async {
      final now = DateTime.now();
      final clock = mockClock(now);
      final workmanager = mockWorkmanager();
      final workmanagerUtils = WorkmanagerUtils();

      await withIOS(
        () => withInjected2<Workmanager, Clock>(
          workmanager,
          clock,
          () => workmanagerUtils
              .registerOneOffTask(WorkmanagerTask.backgroundSync),
        ),
      );

      verify(() => workmanager
          .cancelByUniqueName(WorkmanagerTask.backgroundSync.name)).called(1);
      verify(
        () => workmanager.registerProcessingTask(
          any(),
          any(),
          constraints: any(named: 'constraints'),
          initialDelay: any(named: 'initialDelay'),
        ),
      ).called(1);
    });

    test('registerSyncTask Android', () async {
      final now = DateTime.now();
      final clock = mockClock(now);
      final workmanager = mockWorkmanager();
      final workmanagerUtils = WorkmanagerUtils();

      await withAndroid(
        () => withInjected2<Workmanager, Clock>(
          workmanager,
          clock,
          () => workmanagerUtils
              .registerOneOffTask(WorkmanagerTask.backgroundSync),
        ),
      );

      verifyNever(() =>
          workmanager.cancelByUniqueName(WorkmanagerTask.backgroundSync.name));
      verify(
        () => workmanager.registerOneOffTask(
          any(),
          any(),
          constraints: any(named: 'constraints'),
          existingWorkPolicy: any(named: 'existingWorkPolicy'),
          initialDelay: any(named: 'initialDelay'),
          backoffPolicy: any(named: 'backoffPolicy'),
        ),
      ).called(1);
    });

    test('cancelTask', () async {
      final workmanager = mockWorkmanager();
      final workmanagerUtils = WorkmanagerUtils();

      await withInjected<Workmanager>(
        workmanager,
        () => workmanagerUtils.cancelTask(WorkmanagerTask.backgroundSync),
      );

      verify(() => workmanager
          .cancelByUniqueName(WorkmanagerTask.backgroundSync.name)).called(1);
    });

    test('Dispatches to $BackgroundSync', () async {
      await _testRunsBackgroundSync(true);
    });

    test('Does nothing due to appConfig', () async {
      await _testRunsBackgroundSync(false);
    });

    test('Default periodic task parameters', () async {
      final workmanager = mockWorkmanager();
      final workmanagerUtils = WorkmanagerUtils();
      const task = WorkmanagerTask.periodicTask;

      await withAndroid(
        () => withInjected<Workmanager>(
          workmanager,
          () => workmanagerUtils.registerPeriodicTask(),
        ),
      );

      final constraints = verify(
        () => workmanager.registerOneOffTask(
          task.name,
          task.name,
          constraints: captureAny(named: 'constraints'),
          existingWorkPolicy: ExistingWorkPolicy.replace,
          initialDelay: const Duration(hours: 1),
          backoffPolicy: BackoffPolicy.exponential,
        ),
      ).captured.first as Constraints;
      expect(constraints.networkType, NetworkType.connected);
      expect(constraints.requiresDeviceIdle, null);
    });
  });
}

Future<void> _testRunsBackgroundSync(bool isEnabled) {
  final workmanager = mockWorkmanager();
  const backgroundSyncRunResult = false;
  final cancelableByForeground =
      mockCancelableByForeground(completes: backgroundSyncRunResult);

  return withInjected4<Workmanager, CancelableByForeground, AppConfig,
      BackgroundWorkUtils>(
    workmanager,
    cancelableByForeground,
    mockAppConfig(isBackgroundSyncEnabled: isEnabled),
    mockBackgroundWorkUtils<bool>(),
    () async {
      workmanagerCallbackDispatcher();
      final dispatcher = verify(() => workmanager.executeTask(captureAny()))
          .captured
          .first as BackgroundTaskHandler;

      final result = await dispatcher(
          WorkmanagerTask.backgroundSync.name, <String, dynamic>{});

      expect(result, isEnabled ? backgroundSyncRunResult : true);
      if (isEnabled) {
        verify(() => cancelableByForeground.run(any(), BackgroundSync()))
            .called(1);
      } else {
        verifyNever(() => cancelableByForeground.run(any(), BackgroundSync()));
      }
    },
  );
}
