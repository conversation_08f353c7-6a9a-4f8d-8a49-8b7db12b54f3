import 'package:bitacora/util/background_work/background_provider.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('$BackgroundContext tests', () {
    test('Provides dependency', () {
      final context =
          BackgroundContext([BackgroundProvider<int>(create: (_) => 2)]);

      expect(context.read<int>(), 2);
    });

    test('Provides same dependency', () {
      var count = 0;
      final context = BackgroundContext([
        BackgroundProvider<int>(create: (_) {
          count++;
          return 2;
        })
      ]);

      expect(context.read<int>(), 2);
      expect(context.read<int>(), 2);

      expect(count, 1);
    });
  });
}
