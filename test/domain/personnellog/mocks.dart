import 'package:bitacora/domain/personnellog/personnellog_repository.dart';
import 'package:mocktail/mocktail.dart';

class MockPersonnellogRepository extends Mock
    implements PersonnellogRepository {}

PersonnellogRepository mockPersonnelLogRepository(
    {List<String> names = const <String>[]}) {
  final mock = MockPersonnellogRepository();
  when(() => mock.names(any())).thenAnswer((_) => Future.value(names));
  return mock;
}
