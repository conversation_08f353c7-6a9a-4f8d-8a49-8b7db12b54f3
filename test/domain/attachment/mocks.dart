import 'dart:async';

import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/attachment/attachment_repository.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:mocktail/mocktail.dart';

class MockAttachmentRepository extends Mock implements AttachmentRepository {}

AttachmentRepository mockAttachmentRepository({
  StreamController<Mutation<Attachment>>? mutationsController,
}) {
  final mock = MockAttachmentRepository();
  when(() => mock.getMutations()).thenAnswer((_) {
    final controller = mutationsController ??
        StreamController<Mutation<Attachment>>.broadcast();
    return controller.stream;
  });
  return mock;
}
