import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../util/loader/mocks.dart';

void main() {
  group('$ActiveOrganization tests', () {
    test('ActiveProject key', () {
      final activeOrganization =
          ActiveOrganization(mockLoader<List<Organization>>());

      expect(activeOrganization.prefsKey,
          SharedPreferencesKeys.activeOrganizationId);
      expect(activeOrganization.autoSelectWhenSingle, true);
    });
  });
}
