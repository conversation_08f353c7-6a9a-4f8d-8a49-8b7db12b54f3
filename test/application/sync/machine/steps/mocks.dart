import 'package:bitacora/application/sync/machine/steps/sync_machine_steps_builder.dart';
import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:mocktail/mocktail.dart';

class MockSyncMachineStepsBuilder extends Mo<PERSON>
    implements SyncMachineStepsBuilder {}

SyncMachineStepsBuilder mockSyncMachineStepsBuilder(
    List<SyncMachineStep>? steps) {
  final mock = MockSyncMachineStepsBuilder();
  when(() => mock.buildForBackgroundSync(any()))
      .thenAnswer((_) => Future.value(steps ?? []));
  return mock;
}
