import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../mocktail_fallback_values.dart';
import '_robots/entry_form_date_time_option_test_robot.dart';

void main() {
  group('$EntryFormDateTimeOption tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Ui widgets', (tester) async {
      final robot = EntryFormDateTimeOptionTestRobot(tester);

      await robot.pumpWidget();

      robot.verifyDateTimeRangeUi();
    });

    testWidgets('Switch to Schedule', (tester) async {
      final robot = EntryFormDateTimeOptionTestRobot(tester);
      await robot.pumpWidget();
      robot.verifyDateTimeRangeUi();

      await robot.switchToSchedule();

      robot.verifyOpenStateUi();
    });

    testWidgets('Switch Open State Type', (tester) async {
      final robot = EntryFormDateTimeOptionTestRobot(tester);
      await robot.pumpWidget();
      robot.verifyDateTimeRangeUi();
      await robot.switchToSchedule();

      await robot.switchOpenStateType();

      robot.verifyOpenStateUi('Progressive');
    });
  });
}
