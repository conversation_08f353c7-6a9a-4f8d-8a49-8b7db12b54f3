import 'package:bitacora/presentation/user_settings/speech_to_text_settings.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../base/base_robot.dart';

class SpeechToTextSettingsTestRobot extends BaseRobot {
  SpeechToTextSettingsTestRobot(super.tester);

  @override
  Future<void> pumpWidget() async {
    await pumpTestApp(
      child: const Column(children: [SpeechToTextSettings()]),
    );
  }

  void verifyUi() {
    expect(find.text('Audio Recording'), findsOneWidget);
    expect(find.text('Voice Recognition'), findsOneWidget);
    expect(find.byType(Switch), findsOneWidget);
  }

  Future<void> verifySttSetting(bool sttEnabled) async {
    expect(find.byWidgetPredicate((widget) {
      if (widget is! Switch) {
        return false;
      }

      return sttEnabled ? widget.value : !widget.value;
    }), findsOneWidget);
    final prefs = await SharedPreferences.getInstance();
    expect(
        prefs
            .getBool(SharedPreferencesKeys.isAudioRecordingSpeechToTextEnabled),
        sttEnabled);
  }

  Future<void> tapSwitch() async {
    await tap(find.byType(Switch));
    await tester.pumpAndSettle();
  }
}
