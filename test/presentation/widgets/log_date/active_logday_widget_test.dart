import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/domain/common/value_object/log_day.dart';
import 'package:bitacora/presentation/widgets/log_date/active_logday_widget.dart';
import 'package:bitacora/presentation/widgets/log_date/fancy_date.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../../application/cache/logday/mocks.dart';
import '../../../mocktail_fallback_values.dart';

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
  });

  Widget testApp(child, activeLogDay) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<ActiveLogDay>(
          create: (context) => activeLogDay,
        ),
      ],
      child: MaterialApp(
        home: Localizations(
          locale: const Locale('en'),
          delegates: AppLocalizations.localizationsDelegates,
          child: AppBar(title: child),
        ),
      ),
    );
  }

  group('$ActiveLogdayWidget tests', () {
    testWidgets('Display SizedBox when ActiveLogDay is loading',
        (tester) async {
      final activeLogDay = mockActiveLogDay(hasLoaded: false);

      await tester
          .pumpWidget(testApp(const ActiveLogdayWidget(), activeLogDay));

      expect(find.byType(SizedBox), findsOneWidget);
      expect(find.byType(FancyDate), findsNothing);
    });

    testWidgets('Display FancyDate when ActiveLogDay value is available',
        (tester) async {
      final now = DateTime.now();
      var logDay = LogDay(getLogDayFromDateTime(now));
      final activeLogDay = mockActiveLogDay(logDay: logDay);

      await tester
          .pumpWidget(testApp(const ActiveLogdayWidget(), activeLogDay));

      expect(find.byType(SizedBox), findsNothing);
      expect(find.byType(FancyDate), findsOneWidget);
    });

    testWidgets('Long Press to load current day', (tester) async {
      final now = DateTime.now();
      var logDay = const LogDay(20210129);
      final activeLogDay = mockActiveLogDay(logDay: logDay);
      when(() => activeLogDay.set(any())).thenAnswer((invocation) async {
        logDay = LogDay(invocation.positionalArguments[0].value);
      });
      await tester
          .pumpWidget(testApp(const ActiveLogdayWidget(), activeLogDay));

      final gestureDetector = find.byType(ActiveLogdayWidget);
      await tester.ensureVisible(gestureDetector);
      await tester.longPress(gestureDetector);
      await tester.pumpAndSettle();

      expect(getLogDayFromDateTime(now), logDay.value);
    });

    // FIXME: Verify platform to test specific widgets.
  });
}
