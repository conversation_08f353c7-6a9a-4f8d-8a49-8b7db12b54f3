# The daylog appears. What are the entries?
# The client will query the entire collection

query FetchEntriesByDay(
    $orgId: ID,
    $projectId: ID, # (Either orgId or projectId must be present)
    $day: Day!,
    $cursor: String,
    $includeNextDay: Bool = false,
    $nextDayCursor: String,
    $includePrevDay: Bool = false,
    $prevDayCursor: String,
    $limit: int = 20
    ) {

  thisDay: entriesConnection(orgId:$orgId, projectId:$projectId, day:$day, first:$limit, after:$cursor) {
    node {
      ...entriesFields
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }

  nextDay: entriesConnection(orgId:$orgId, projectId:$projectId, dayAfter:$day, first: $limit, after:$nextDayCursor) {
    node {
      ...entriesFields
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }

  prevDay: entriesConnection(orgId:$orgId, projectId:$projectId, dayBefore:$day, first: $limit, after:$prevDayCursor) {
    node {
      ...entriesFields
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }

}

fragment entriesPageInfo {
  endCursor
  hasNextPage
}

fragment entriesFields on Entry {
  id
  day
  comments
  assignee {
    ...assigneeFields
  }
  openState {
    ...openStateFields
  }
  extension {
    ...inventorylogFields
    ...worklogFields
    ...personnellogFields
  }
  attachments {
    ...attachmentFields
  }
}

fragment assigneeFields on User {
  id
  name
}

fragment openStateFields on User {
  id
  progress
  progressive
  endDay
}

fragment inventorylogFields {
  itemName
  quantity
  provider
  destSublocation
  sourceSublocation
  costPrice
  salePrice
  paymentStatus
  priceIsUnit
  reason
  type
  destProject {
    ...projectFields
  }
  sourceProject {
    ...projectFields
  }
}

fragment projectFields {
  id
  name
}

fragment attachmentFields {
  s3Key
}

...
